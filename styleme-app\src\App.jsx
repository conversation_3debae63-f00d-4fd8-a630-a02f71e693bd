import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Upload, Download, Refresh<PERSON><PERSON>, <PERSON><PERSON><PERSON>, User, <PERSON><PERSON>, Zap } from 'lucide-react'
import { API_CONFIG, getApi<PERSON>ey, createFashionPrompt, prepareImagesForAPI } from './config/api'
import './App.css'

function App() {
  const [userPhoto, setUserPhoto] = useState(null)
  const [clothingItems, setClothingItems] = useState({
    head: null,
    torso: null,
    legs: null
  })
  const [generatedImage, setGeneratedImage] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  const handleUserPhotoUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => setUserPhoto(e.target.result)
      reader.readAsDataURL(file)
    }
  }

  const handleClothingUpload = (slot, event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setClothingItems(prev => ({
          ...prev,
          [slot]: e.target.result
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  const generateStylePreview = async () => {
    if (!userPhoto) {
      setError('Carica prima la tua foto!')
      return
    }

    const hasClothing = Object.values(clothingItems).some(item => item !== null)
    if (!hasClothing) {
      setError('Carica almeno un indumento!')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const apiKey = getApiKey()

      // Prepara le immagini per l'API
      const images = prepareImagesForAPI(userPhoto, clothingItems)

      // Crea il prompt per il modello
      const prompt = createFashionPrompt(clothingItems)

      const response = await fetch(`${API_CONFIG.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          ...API_CONFIG.headers
        },
        body: JSON.stringify({
          model: API_CONFIG.model,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt
                },
                ...images.map(image => ({
                  type: 'image_url',
                  image_url: {
                    url: image
                  }
                }))
              ]
            }
          ],
          max_tokens: API_CONFIG.maxTokens
        })
      })

      if (!response.ok) {
        throw new Error(`Errore API: ${response.status}`)
      }

      const data = await response.json()

      // Per ora simuliamo la risposta dato che Gemini 2.5 Flash Image Preview potrebbe non generare immagini
      // In una implementazione reale, dovresti gestire la risposta dell'API appropriatamente
      setGeneratedImage(userPhoto) // Placeholder - sostituisci con l'immagine generata dall'API

    } catch (err) {
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const downloadImage = () => {
    if (generatedImage) {
      const link = document.createElement('a')
      link.href = generatedImage
      link.download = 'styleme-preview.png'
      link.click()
    }
  }

  const resetApp = () => {
    setUserPhoto(null)
    setClothingItems({ head: null, torso: null, legs: null })
    setGeneratedImage(null)
    setError(null)
  }

  return (
    <div className="app">
      <div className="background-pattern"></div>

      <header className="header">
        <motion.div
          className="logo"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Sparkles className="logo-icon" />
          <h1>StyleMe</h1>
          <p>AI Fashion Preview</p>
        </motion.div>
      </header>

      <main className="main-content">
        {!generatedImage ? (
          <div className="upload-section">
            {/* User Photo Upload */}
            <motion.div
              className="upload-card user-photo-card"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="upload-header">
                <User className="upload-icon" />
                <h3>La Tua Foto</h3>
              </div>

              {userPhoto ? (
                <div className="photo-preview">
                  <img src={userPhoto} alt="User" />
                  <button
                    className="change-photo-btn"
                    onClick={() => document.getElementById('user-photo').click()}
                  >
                    Cambia Foto
                  </button>
                </div>
              ) : (
                <label className="upload-area" htmlFor="user-photo">
                  <Upload className="upload-placeholder-icon" />
                  <span>Carica la tua foto</span>
                  <small>Foto intera o mezzo busto</small>
                </label>
              )}

              <input
                id="user-photo"
                type="file"
                accept="image/*"
                onChange={handleUserPhotoUpload}
                style={{ display: 'none' }}
              />
            </motion.div>

            {/* Clothing Slots */}
            <div className="clothing-slots">
              {[
                { key: 'head', label: 'Testa', icon: '👑' },
                { key: 'torso', label: 'Busto', icon: '👕' },
                { key: 'legs', label: 'Gambe', icon: '👖' }
              ].map((slot, index) => (
                <motion.div
                  key={slot.key}
                  className="upload-card clothing-card"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="upload-header">
                    <span className="clothing-emoji">{slot.icon}</span>
                    <h4>{slot.label}</h4>
                  </div>

                  {clothingItems[slot.key] ? (
                    <div className="clothing-preview">
                      <img src={clothingItems[slot.key]} alt={slot.label} />
                      <button
                        className="change-clothing-btn"
                        onClick={() => document.getElementById(`clothing-${slot.key}`).click()}
                      >
                        Cambia
                      </button>
                    </div>
                  ) : (
                    <label className="upload-area small" htmlFor={`clothing-${slot.key}`}>
                      <Upload className="upload-placeholder-icon small" />
                      <span>Carica {slot.label.toLowerCase()}</span>
                    </label>
                  )}

                  <input
                    id={`clothing-${slot.key}`}
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleClothingUpload(slot.key, e)}
                    style={{ display: 'none' }}
                  />
                </motion.div>
              ))}
            </div>

            {/* Generate Button */}
            <motion.button
              className="generate-btn"
              onClick={generateStylePreview}
              disabled={isLoading || !userPhoto}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="btn-icon spinning" />
                  Generando...
                </>
              ) : (
                <>
                  <Zap className="btn-icon" />
                  Genera Preview
                </>
              )}
            </motion.button>

            {error && (
              <motion.div
                className="error-message"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                {error}
              </motion.div>
            )}
          </div>
        ) : (
          <motion.div
            className="result-section"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <div className="result-header">
              <h2>La Tua Preview di Stile</h2>
            </div>

            <div className="result-image">
              <img src={generatedImage} alt="Style Preview" />
            </div>

            <div className="result-actions">
              <motion.button
                className="download-btn"
                onClick={downloadImage}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Download className="btn-icon" />
                Scarica Immagine
              </motion.button>

              <motion.button
                className="retry-btn"
                onClick={resetApp}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <RefreshCw className="btn-icon" />
                Riprova
              </motion.button>
            </div>
          </motion.div>
        )}
      </main>
    </div>
  )
}

export default App
