(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))o(c);new MutationObserver(c=>{for(const d of c)if(d.type==="childList")for(const h of d.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&o(h)}).observe(document,{childList:!0,subtree:!0});function u(c){const d={};return c.integrity&&(d.integrity=c.integrity),c.referrerPolicy&&(d.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?d.credentials="include":c.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function o(c){if(c.ep)return;c.ep=!0;const d=u(c);fetch(c.href,d)}})();var gr={exports:{}},ll={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zm;function e1(){if(zm)return ll;zm=1;var i=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function u(o,c,d){var h=null;if(d!==void 0&&(h=""+d),c.key!==void 0&&(h=""+c.key),"key"in c){d={};for(var p in c)p!=="key"&&(d[p]=c[p])}else d=c;return c=d.ref,{$$typeof:i,type:o,key:h,ref:c!==void 0?c:null,props:d}}return ll.Fragment=l,ll.jsx=u,ll.jsxs=u,ll}var _m;function n1(){return _m||(_m=1,gr.exports=e1()),gr.exports}var J=n1(),vr={exports:{}},st={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Um;function a1(){if(Um)return st;Um=1;var i=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),h=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),A=Symbol.iterator;function x(T){return T===null||typeof T!="object"?null:(T=A&&T[A]||T["@@iterator"],typeof T=="function"?T:null)}var U={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},w=Object.assign,L={};function Y(T,N,Z){this.props=T,this.context=N,this.refs=L,this.updater=Z||U}Y.prototype.isReactComponent={},Y.prototype.setState=function(T,N){if(typeof T!="object"&&typeof T!="function"&&T!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,T,N,"setState")},Y.prototype.forceUpdate=function(T){this.updater.enqueueForceUpdate(this,T,"forceUpdate")};function q(){}q.prototype=Y.prototype;function X(T,N,Z){this.props=T,this.context=N,this.refs=L,this.updater=Z||U}var H=X.prototype=new q;H.constructor=X,w(H,Y.prototype),H.isPureReactComponent=!0;var Q=Array.isArray,B={H:null,A:null,T:null,S:null,V:null},tt=Object.prototype.hasOwnProperty;function it(T,N,Z,G,$,dt){return Z=dt.ref,{$$typeof:i,type:T,key:N,ref:Z!==void 0?Z:null,props:dt}}function F(T,N){return it(T.type,N,void 0,void 0,void 0,T.props)}function xt(T){return typeof T=="object"&&T!==null&&T.$$typeof===i}function Lt(T){var N={"=":"=0",":":"=2"};return"$"+T.replace(/[=:]/g,function(Z){return N[Z]})}var Wt=/\/+/g;function Ht(T,N){return typeof T=="object"&&T!==null&&T.key!=null?Lt(""+T.key):N.toString(36)}function Qe(){}function je(T){switch(T.status){case"fulfilled":return T.value;case"rejected":throw T.reason;default:switch(typeof T.status=="string"?T.then(Qe,Qe):(T.status="pending",T.then(function(N){T.status==="pending"&&(T.status="fulfilled",T.value=N)},function(N){T.status==="pending"&&(T.status="rejected",T.reason=N)})),T.status){case"fulfilled":return T.value;case"rejected":throw T.reason}}throw T}function qt(T,N,Z,G,$){var dt=typeof T;(dt==="undefined"||dt==="boolean")&&(T=null);var lt=!1;if(T===null)lt=!0;else switch(dt){case"bigint":case"string":case"number":lt=!0;break;case"object":switch(T.$$typeof){case i:case l:lt=!0;break;case v:return lt=T._init,qt(lt(T._payload),N,Z,G,$)}}if(lt)return $=$(T),lt=G===""?"."+Ht(T,0):G,Q($)?(Z="",lt!=null&&(Z=lt.replace(Wt,"$&/")+"/"),qt($,N,Z,"",function(hn){return hn})):$!=null&&(xt($)&&($=F($,Z+($.key==null||T&&T.key===$.key?"":(""+$.key).replace(Wt,"$&/")+"/")+lt)),N.push($)),1;lt=0;var ce=G===""?".":G+":";if(Q(T))for(var Et=0;Et<T.length;Et++)G=T[Et],dt=ce+Ht(G,Et),lt+=qt(G,N,Z,dt,$);else if(Et=x(T),typeof Et=="function")for(T=Et.call(T),Et=0;!(G=T.next()).done;)G=G.value,dt=ce+Ht(G,Et++),lt+=qt(G,N,Z,dt,$);else if(dt==="object"){if(typeof T.then=="function")return qt(je(T),N,Z,G,$);throw N=String(T),Error("Objects are not valid as a React child (found: "+(N==="[object Object]"?"object with keys {"+Object.keys(T).join(", ")+"}":N)+"). If you meant to render a collection of children, use an array instead.")}return lt}function V(T,N,Z){if(T==null)return T;var G=[],$=0;return qt(T,G,"","",function(dt){return N.call(Z,dt,$++)}),G}function j(T){if(T._status===-1){var N=T._result;N=N(),N.then(function(Z){(T._status===0||T._status===-1)&&(T._status=1,T._result=Z)},function(Z){(T._status===0||T._status===-1)&&(T._status=2,T._result=Z)}),T._status===-1&&(T._status=0,T._result=N)}if(T._status===1)return T._result.default;throw T._result}var k=typeof reportError=="function"?reportError:function(T){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var N=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof T=="object"&&T!==null&&typeof T.message=="string"?String(T.message):String(T),error:T});if(!window.dispatchEvent(N))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",T);return}console.error(T)};function ft(){}return st.Children={map:V,forEach:function(T,N,Z){V(T,function(){N.apply(this,arguments)},Z)},count:function(T){var N=0;return V(T,function(){N++}),N},toArray:function(T){return V(T,function(N){return N})||[]},only:function(T){if(!xt(T))throw Error("React.Children.only expected to receive a single React element child.");return T}},st.Component=Y,st.Fragment=u,st.Profiler=c,st.PureComponent=X,st.StrictMode=o,st.Suspense=g,st.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=B,st.__COMPILER_RUNTIME={__proto__:null,c:function(T){return B.H.useMemoCache(T)}},st.cache=function(T){return function(){return T.apply(null,arguments)}},st.cloneElement=function(T,N,Z){if(T==null)throw Error("The argument must be a React element, but you passed "+T+".");var G=w({},T.props),$=T.key,dt=void 0;if(N!=null)for(lt in N.ref!==void 0&&(dt=void 0),N.key!==void 0&&($=""+N.key),N)!tt.call(N,lt)||lt==="key"||lt==="__self"||lt==="__source"||lt==="ref"&&N.ref===void 0||(G[lt]=N[lt]);var lt=arguments.length-2;if(lt===1)G.children=Z;else if(1<lt){for(var ce=Array(lt),Et=0;Et<lt;Et++)ce[Et]=arguments[Et+2];G.children=ce}return it(T.type,$,void 0,void 0,dt,G)},st.createContext=function(T){return T={$$typeof:h,_currentValue:T,_currentValue2:T,_threadCount:0,Provider:null,Consumer:null},T.Provider=T,T.Consumer={$$typeof:d,_context:T},T},st.createElement=function(T,N,Z){var G,$={},dt=null;if(N!=null)for(G in N.key!==void 0&&(dt=""+N.key),N)tt.call(N,G)&&G!=="key"&&G!=="__self"&&G!=="__source"&&($[G]=N[G]);var lt=arguments.length-2;if(lt===1)$.children=Z;else if(1<lt){for(var ce=Array(lt),Et=0;Et<lt;Et++)ce[Et]=arguments[Et+2];$.children=ce}if(T&&T.defaultProps)for(G in lt=T.defaultProps,lt)$[G]===void 0&&($[G]=lt[G]);return it(T,dt,void 0,void 0,null,$)},st.createRef=function(){return{current:null}},st.forwardRef=function(T){return{$$typeof:p,render:T}},st.isValidElement=xt,st.lazy=function(T){return{$$typeof:v,_payload:{_status:-1,_result:T},_init:j}},st.memo=function(T,N){return{$$typeof:y,type:T,compare:N===void 0?null:N}},st.startTransition=function(T){var N=B.T,Z={};B.T=Z;try{var G=T(),$=B.S;$!==null&&$(Z,G),typeof G=="object"&&G!==null&&typeof G.then=="function"&&G.then(ft,k)}catch(dt){k(dt)}finally{B.T=N}},st.unstable_useCacheRefresh=function(){return B.H.useCacheRefresh()},st.use=function(T){return B.H.use(T)},st.useActionState=function(T,N,Z){return B.H.useActionState(T,N,Z)},st.useCallback=function(T,N){return B.H.useCallback(T,N)},st.useContext=function(T){return B.H.useContext(T)},st.useDebugValue=function(){},st.useDeferredValue=function(T,N){return B.H.useDeferredValue(T,N)},st.useEffect=function(T,N,Z){var G=B.H;if(typeof Z=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return G.useEffect(T,N)},st.useId=function(){return B.H.useId()},st.useImperativeHandle=function(T,N,Z){return B.H.useImperativeHandle(T,N,Z)},st.useInsertionEffect=function(T,N){return B.H.useInsertionEffect(T,N)},st.useLayoutEffect=function(T,N){return B.H.useLayoutEffect(T,N)},st.useMemo=function(T,N){return B.H.useMemo(T,N)},st.useOptimistic=function(T,N){return B.H.useOptimistic(T,N)},st.useReducer=function(T,N,Z){return B.H.useReducer(T,N,Z)},st.useRef=function(T){return B.H.useRef(T)},st.useState=function(T){return B.H.useState(T)},st.useSyncExternalStore=function(T,N,Z){return B.H.useSyncExternalStore(T,N,Z)},st.useTransition=function(){return B.H.useTransition()},st.version="19.1.1",st}var Nm;function lc(){return Nm||(Nm=1,vr.exports=a1()),vr.exports}var W=lc(),Sr={exports:{}},sl={},Tr={exports:{}},br={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bm;function i1(){return Bm||(Bm=1,(function(i){function l(V,j){var k=V.length;V.push(j);t:for(;0<k;){var ft=k-1>>>1,T=V[ft];if(0<c(T,j))V[ft]=j,V[k]=T,k=ft;else break t}}function u(V){return V.length===0?null:V[0]}function o(V){if(V.length===0)return null;var j=V[0],k=V.pop();if(k!==j){V[0]=k;t:for(var ft=0,T=V.length,N=T>>>1;ft<N;){var Z=2*(ft+1)-1,G=V[Z],$=Z+1,dt=V[$];if(0>c(G,k))$<T&&0>c(dt,G)?(V[ft]=dt,V[$]=k,ft=$):(V[ft]=G,V[Z]=k,ft=Z);else if($<T&&0>c(dt,k))V[ft]=dt,V[$]=k,ft=$;else break t}}return j}function c(V,j){var k=V.sortIndex-j.sortIndex;return k!==0?k:V.id-j.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;i.unstable_now=function(){return d.now()}}else{var h=Date,p=h.now();i.unstable_now=function(){return h.now()-p}}var g=[],y=[],v=1,A=null,x=3,U=!1,w=!1,L=!1,Y=!1,q=typeof setTimeout=="function"?setTimeout:null,X=typeof clearTimeout=="function"?clearTimeout:null,H=typeof setImmediate<"u"?setImmediate:null;function Q(V){for(var j=u(y);j!==null;){if(j.callback===null)o(y);else if(j.startTime<=V)o(y),j.sortIndex=j.expirationTime,l(g,j);else break;j=u(y)}}function B(V){if(L=!1,Q(V),!w)if(u(g)!==null)w=!0,tt||(tt=!0,Ht());else{var j=u(y);j!==null&&qt(B,j.startTime-V)}}var tt=!1,it=-1,F=5,xt=-1;function Lt(){return Y?!0:!(i.unstable_now()-xt<F)}function Wt(){if(Y=!1,tt){var V=i.unstable_now();xt=V;var j=!0;try{t:{w=!1,L&&(L=!1,X(it),it=-1),U=!0;var k=x;try{e:{for(Q(V),A=u(g);A!==null&&!(A.expirationTime>V&&Lt());){var ft=A.callback;if(typeof ft=="function"){A.callback=null,x=A.priorityLevel;var T=ft(A.expirationTime<=V);if(V=i.unstable_now(),typeof T=="function"){A.callback=T,Q(V),j=!0;break e}A===u(g)&&o(g),Q(V)}else o(g);A=u(g)}if(A!==null)j=!0;else{var N=u(y);N!==null&&qt(B,N.startTime-V),j=!1}}break t}finally{A=null,x=k,U=!1}j=void 0}}finally{j?Ht():tt=!1}}}var Ht;if(typeof H=="function")Ht=function(){H(Wt)};else if(typeof MessageChannel<"u"){var Qe=new MessageChannel,je=Qe.port2;Qe.port1.onmessage=Wt,Ht=function(){je.postMessage(null)}}else Ht=function(){q(Wt,0)};function qt(V,j){it=q(function(){V(i.unstable_now())},j)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(V){V.callback=null},i.unstable_forceFrameRate=function(V){0>V||125<V?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<V?Math.floor(1e3/V):5},i.unstable_getCurrentPriorityLevel=function(){return x},i.unstable_next=function(V){switch(x){case 1:case 2:case 3:var j=3;break;default:j=x}var k=x;x=j;try{return V()}finally{x=k}},i.unstable_requestPaint=function(){Y=!0},i.unstable_runWithPriority=function(V,j){switch(V){case 1:case 2:case 3:case 4:case 5:break;default:V=3}var k=x;x=V;try{return j()}finally{x=k}},i.unstable_scheduleCallback=function(V,j,k){var ft=i.unstable_now();switch(typeof k=="object"&&k!==null?(k=k.delay,k=typeof k=="number"&&0<k?ft+k:ft):k=ft,V){case 1:var T=-1;break;case 2:T=250;break;case 5:T=1073741823;break;case 4:T=1e4;break;default:T=5e3}return T=k+T,V={id:v++,callback:j,priorityLevel:V,startTime:k,expirationTime:T,sortIndex:-1},k>ft?(V.sortIndex=k,l(y,V),u(g)===null&&V===u(y)&&(L?(X(it),it=-1):L=!0,qt(B,k-ft))):(V.sortIndex=T,l(g,V),w||U||(w=!0,tt||(tt=!0,Ht()))),V},i.unstable_shouldYield=Lt,i.unstable_wrapCallback=function(V){var j=x;return function(){var k=x;x=j;try{return V.apply(this,arguments)}finally{x=k}}}})(br)),br}var jm;function l1(){return jm||(jm=1,Tr.exports=i1()),Tr.exports}var Ar={exports:{}},te={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wm;function s1(){if(wm)return te;wm=1;var i=lc();function l(g){var y="https://react.dev/errors/"+g;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)y+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+g+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var o={d:{f:u,r:function(){throw Error(l(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},c=Symbol.for("react.portal");function d(g,y,v){var A=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:A==null?null:""+A,children:g,containerInfo:y,implementation:v}}var h=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(g,y){if(g==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return te.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,te.createPortal=function(g,y){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(l(299));return d(g,y,null,v)},te.flushSync=function(g){var y=h.T,v=o.p;try{if(h.T=null,o.p=2,g)return g()}finally{h.T=y,o.p=v,o.d.f()}},te.preconnect=function(g,y){typeof g=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,o.d.C(g,y))},te.prefetchDNS=function(g){typeof g=="string"&&o.d.D(g)},te.preinit=function(g,y){if(typeof g=="string"&&y&&typeof y.as=="string"){var v=y.as,A=p(v,y.crossOrigin),x=typeof y.integrity=="string"?y.integrity:void 0,U=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;v==="style"?o.d.S(g,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:A,integrity:x,fetchPriority:U}):v==="script"&&o.d.X(g,{crossOrigin:A,integrity:x,fetchPriority:U,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},te.preinitModule=function(g,y){if(typeof g=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var v=p(y.as,y.crossOrigin);o.d.M(g,{crossOrigin:v,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&o.d.M(g)},te.preload=function(g,y){if(typeof g=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var v=y.as,A=p(v,y.crossOrigin);o.d.L(g,v,{crossOrigin:A,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},te.preloadModule=function(g,y){if(typeof g=="string")if(y){var v=p(y.as,y.crossOrigin);o.d.m(g,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:v,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else o.d.m(g)},te.requestFormReset=function(g){o.d.r(g)},te.unstable_batchedUpdates=function(g,y){return g(y)},te.useFormState=function(g,y,v){return h.H.useFormState(g,y,v)},te.useFormStatus=function(){return h.H.useHostTransitionStatus()},te.version="19.1.1",te}var Lm;function u1(){if(Lm)return Ar.exports;Lm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(l){console.error(l)}}return i(),Ar.exports=s1(),Ar.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hm;function o1(){if(Hm)return sl;Hm=1;var i=l1(),l=lc(),u=u1();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function h(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function p(t){if(d(t)!==t)throw Error(o(188))}function g(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(o(188));return e!==t?null:t}for(var n=t,a=e;;){var s=n.return;if(s===null)break;var r=s.alternate;if(r===null){if(a=s.return,a!==null){n=a;continue}break}if(s.child===r.child){for(r=s.child;r;){if(r===n)return p(s),t;if(r===a)return p(s),e;r=r.sibling}throw Error(o(188))}if(n.return!==a.return)n=s,a=r;else{for(var f=!1,m=s.child;m;){if(m===n){f=!0,n=s,a=r;break}if(m===a){f=!0,a=s,n=r;break}m=m.sibling}if(!f){for(m=r.child;m;){if(m===n){f=!0,n=r,a=s;break}if(m===a){f=!0,a=r,n=s;break}m=m.sibling}if(!f)throw Error(o(189))}}if(n.alternate!==a)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?t:e}function y(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=y(t),e!==null)return e;t=t.sibling}return null}var v=Object.assign,A=Symbol.for("react.element"),x=Symbol.for("react.transitional.element"),U=Symbol.for("react.portal"),w=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),Y=Symbol.for("react.profiler"),q=Symbol.for("react.provider"),X=Symbol.for("react.consumer"),H=Symbol.for("react.context"),Q=Symbol.for("react.forward_ref"),B=Symbol.for("react.suspense"),tt=Symbol.for("react.suspense_list"),it=Symbol.for("react.memo"),F=Symbol.for("react.lazy"),xt=Symbol.for("react.activity"),Lt=Symbol.for("react.memo_cache_sentinel"),Wt=Symbol.iterator;function Ht(t){return t===null||typeof t!="object"?null:(t=Wt&&t[Wt]||t["@@iterator"],typeof t=="function"?t:null)}var Qe=Symbol.for("react.client.reference");function je(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Qe?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case w:return"Fragment";case Y:return"Profiler";case L:return"StrictMode";case B:return"Suspense";case tt:return"SuspenseList";case xt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case U:return"Portal";case H:return(t.displayName||"Context")+".Provider";case X:return(t._context.displayName||"Context")+".Consumer";case Q:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case it:return e=t.displayName||null,e!==null?e:je(t.type)||"Memo";case F:e=t._payload,t=t._init;try{return je(t(e))}catch{}}return null}var qt=Array.isArray,V=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,j=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k={pending:!1,data:null,method:null,action:null},ft=[],T=-1;function N(t){return{current:t}}function Z(t){0>T||(t.current=ft[T],ft[T]=null,T--)}function G(t,e){T++,ft[T]=t.current,t.current=e}var $=N(null),dt=N(null),lt=N(null),ce=N(null);function Et(t,e){switch(G(lt,e),G(dt,t),G($,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?lm(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=lm(e),t=sm(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Z($),G($,t)}function hn(){Z($),Z(dt),Z(lt)}function eu(t){t.memoizedState!==null&&G(ce,t);var e=$.current,n=sm(e,t.type);e!==n&&(G(dt,t),G($,n))}function El(t){dt.current===t&&(Z($),Z(dt)),ce.current===t&&(Z(ce),tl._currentValue=k)}var nu=Object.prototype.hasOwnProperty,au=i.unstable_scheduleCallback,iu=i.unstable_cancelCallback,Ug=i.unstable_shouldYield,Ng=i.unstable_requestPaint,we=i.unstable_now,Bg=i.unstable_getCurrentPriorityLevel,Lc=i.unstable_ImmediatePriority,Hc=i.unstable_UserBlockingPriority,Ml=i.unstable_NormalPriority,jg=i.unstable_LowPriority,qc=i.unstable_IdlePriority,wg=i.log,Lg=i.unstable_setDisableYieldValue,oi=null,fe=null;function dn(t){if(typeof wg=="function"&&Lg(t),fe&&typeof fe.setStrictMode=="function")try{fe.setStrictMode(oi,t)}catch{}}var he=Math.clz32?Math.clz32:Yg,Hg=Math.log,qg=Math.LN2;function Yg(t){return t>>>=0,t===0?32:31-(Hg(t)/qg|0)|0}var Dl=256,Rl=4194304;function Yn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Ol(t,e,n){var a=t.pendingLanes;if(a===0)return 0;var s=0,r=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var m=a&134217727;return m!==0?(a=m&~r,a!==0?s=Yn(a):(f&=m,f!==0?s=Yn(f):n||(n=m&~t,n!==0&&(s=Yn(n))))):(m=a&~r,m!==0?s=Yn(m):f!==0?s=Yn(f):n||(n=a&~t,n!==0&&(s=Yn(n)))),s===0?0:e!==0&&e!==s&&(e&r)===0&&(r=s&-s,n=e&-e,r>=n||r===32&&(n&4194048)!==0)?e:s}function ri(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Gg(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Yc(){var t=Dl;return Dl<<=1,(Dl&4194048)===0&&(Dl=256),t}function Gc(){var t=Rl;return Rl<<=1,(Rl&62914560)===0&&(Rl=4194304),t}function lu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function ci(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Xg(t,e,n,a,s,r){var f=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var m=t.entanglements,S=t.expirationTimes,D=t.hiddenUpdates;for(n=f&~n;0<n;){var C=31-he(n),_=1<<C;m[C]=0,S[C]=-1;var R=D[C];if(R!==null)for(D[C]=null,C=0;C<R.length;C++){var O=R[C];O!==null&&(O.lane&=-536870913)}n&=~_}a!==0&&Xc(t,a,0),r!==0&&s===0&&t.tag!==0&&(t.suspendedLanes|=r&~(f&~e))}function Xc(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-he(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|n&4194090}function Zc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var a=31-he(n),s=1<<a;s&e|t[a]&e&&(t[a]|=e),n&=~s}}function su(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function uu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Kc(){var t=j.p;return t!==0?t:(t=window.event,t===void 0?32:Mm(t.type))}function Zg(t,e){var n=j.p;try{return j.p=t,e()}finally{j.p=n}}var mn=Math.random().toString(36).slice(2),$t="__reactFiber$"+mn,ie="__reactProps$"+mn,ha="__reactContainer$"+mn,ou="__reactEvents$"+mn,Kg="__reactListeners$"+mn,Qg="__reactHandles$"+mn,Qc="__reactResources$"+mn,fi="__reactMarker$"+mn;function ru(t){delete t[$t],delete t[ie],delete t[ou],delete t[Kg],delete t[Qg]}function da(t){var e=t[$t];if(e)return e;for(var n=t.parentNode;n;){if(e=n[ha]||n[$t]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=cm(t);t!==null;){if(n=t[$t])return n;t=cm(t)}return e}t=n,n=t.parentNode}return null}function ma(t){if(t=t[$t]||t[ha]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function hi(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function ya(t){var e=t[Qc];return e||(e=t[Qc]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Xt(t){t[fi]=!0}var kc=new Set,Jc={};function Gn(t,e){pa(t,e),pa(t+"Capture",e)}function pa(t,e){for(Jc[t]=e,t=0;t<e.length;t++)kc.add(e[t])}var kg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Pc={},Fc={};function Jg(t){return nu.call(Fc,t)?!0:nu.call(Pc,t)?!1:kg.test(t)?Fc[t]=!0:(Pc[t]=!0,!1)}function Cl(t,e,n){if(Jg(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Vl(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function ke(t,e,n,a){if(a===null)t.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+a)}}var cu,Wc;function ga(t){if(cu===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);cu=e&&e[1]||"",Wc=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+cu+t+Wc}var fu=!1;function hu(t,e){if(!t||fu)return"";fu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var _=function(){throw Error()};if(Object.defineProperty(_.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(_,[])}catch(O){var R=O}Reflect.construct(t,[],_)}else{try{_.call()}catch(O){R=O}t.call(_.prototype)}}else{try{throw Error()}catch(O){R=O}(_=t())&&typeof _.catch=="function"&&_.catch(function(){})}}catch(O){if(O&&R&&typeof O.stack=="string")return[O.stack,R.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=a.DetermineComponentFrameRoot(),f=r[0],m=r[1];if(f&&m){var S=f.split(`
`),D=m.split(`
`);for(s=a=0;a<S.length&&!S[a].includes("DetermineComponentFrameRoot");)a++;for(;s<D.length&&!D[s].includes("DetermineComponentFrameRoot");)s++;if(a===S.length||s===D.length)for(a=S.length-1,s=D.length-1;1<=a&&0<=s&&S[a]!==D[s];)s--;for(;1<=a&&0<=s;a--,s--)if(S[a]!==D[s]){if(a!==1||s!==1)do if(a--,s--,0>s||S[a]!==D[s]){var C=`
`+S[a].replace(" at new "," at ");return t.displayName&&C.includes("<anonymous>")&&(C=C.replace("<anonymous>",t.displayName)),C}while(1<=a&&0<=s);break}}}finally{fu=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?ga(n):""}function Pg(t){switch(t.tag){case 26:case 27:case 5:return ga(t.type);case 16:return ga("Lazy");case 13:return ga("Suspense");case 19:return ga("SuspenseList");case 0:case 15:return hu(t.type,!1);case 11:return hu(t.type.render,!1);case 1:return hu(t.type,!0);case 31:return ga("Activity");default:return""}}function $c(t){try{var e="";do e+=Pg(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Te(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Ic(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Fg(t){var e=Ic(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(f){a=""+f,r.call(this,f)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(f){a=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function zl(t){t._valueTracker||(t._valueTracker=Fg(t))}function tf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),a="";return t&&(a=Ic(t)?t.checked?"true":"false":t.value),t=a,t!==n?(e.setValue(t),!0):!1}function _l(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Wg=/[\n"\\]/g;function be(t){return t.replace(Wg,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function du(t,e,n,a,s,r,f,m){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Te(e)):t.value!==""+Te(e)&&(t.value=""+Te(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?mu(t,f,Te(e)):n!=null?mu(t,f,Te(n)):a!=null&&t.removeAttribute("value"),s==null&&r!=null&&(t.defaultChecked=!!r),s!=null&&(t.checked=s&&typeof s!="function"&&typeof s!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?t.name=""+Te(m):t.removeAttribute("name")}function ef(t,e,n,a,s,r,f,m){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.type=r),e!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||e!=null))return;n=n!=null?""+Te(n):"",e=e!=null?""+Te(e):n,m||e===t.value||(t.value=e),t.defaultValue=e}a=a??s,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=m?t.checked:!!a,t.defaultChecked=!!a,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function mu(t,e,n){e==="number"&&_l(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function va(t,e,n,a){if(t=t.options,e){e={};for(var s=0;s<n.length;s++)e["$"+n[s]]=!0;for(n=0;n<t.length;n++)s=e.hasOwnProperty("$"+t[n].value),t[n].selected!==s&&(t[n].selected=s),s&&a&&(t[n].defaultSelected=!0)}else{for(n=""+Te(n),e=null,s=0;s<t.length;s++){if(t[s].value===n){t[s].selected=!0,a&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function nf(t,e,n){if(e!=null&&(e=""+Te(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Te(n):""}function af(t,e,n,a){if(e==null){if(a!=null){if(n!=null)throw Error(o(92));if(qt(a)){if(1<a.length)throw Error(o(93));a=a[0]}n=a}n==null&&(n=""),e=n}n=Te(e),t.defaultValue=n,a=t.textContent,a===n&&a!==""&&a!==null&&(t.value=a)}function Sa(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var $g=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function lf(t,e,n){var a=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,n):typeof n!="number"||n===0||$g.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function sf(t,e,n){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,n!=null){for(var a in n)!n.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var s in e)a=e[s],e.hasOwnProperty(s)&&n[s]!==a&&lf(t,s,a)}else for(var r in e)e.hasOwnProperty(r)&&lf(t,r,e[r])}function yu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ig=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),t0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ul(t){return t0.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var pu=null;function gu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ta=null,ba=null;function uf(t){var e=ma(t);if(e&&(t=e.stateNode)){var n=t[ie]||null;t:switch(t=e.stateNode,e.type){case"input":if(du(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+be(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var a=n[e];if(a!==t&&a.form===t.form){var s=a[ie]||null;if(!s)throw Error(o(90));du(a,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(e=0;e<n.length;e++)a=n[e],a.form===t.form&&tf(a)}break t;case"textarea":nf(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&va(t,!!n.multiple,e,!1)}}}var vu=!1;function of(t,e,n){if(vu)return t(e,n);vu=!0;try{var a=t(e);return a}finally{if(vu=!1,(Ta!==null||ba!==null)&&(vs(),Ta&&(e=Ta,t=ba,ba=Ta=null,uf(e),t)))for(e=0;e<t.length;e++)uf(t[e])}}function di(t,e){var n=t.stateNode;if(n===null)return null;var a=n[ie]||null;if(a===null)return null;n=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(o(231,e,typeof n));return n}var Je=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Su=!1;if(Je)try{var mi={};Object.defineProperty(mi,"passive",{get:function(){Su=!0}}),window.addEventListener("test",mi,mi),window.removeEventListener("test",mi,mi)}catch{Su=!1}var yn=null,Tu=null,Nl=null;function rf(){if(Nl)return Nl;var t,e=Tu,n=e.length,a,s="value"in yn?yn.value:yn.textContent,r=s.length;for(t=0;t<n&&e[t]===s[t];t++);var f=n-t;for(a=1;a<=f&&e[n-a]===s[r-a];a++);return Nl=s.slice(t,1<a?1-a:void 0)}function Bl(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function jl(){return!0}function cf(){return!1}function le(t){function e(n,a,s,r,f){this._reactName=n,this._targetInst=s,this.type=a,this.nativeEvent=r,this.target=f,this.currentTarget=null;for(var m in t)t.hasOwnProperty(m)&&(n=t[m],this[m]=n?n(r):r[m]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?jl:cf,this.isPropagationStopped=cf,this}return v(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=jl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=jl)},persist:function(){},isPersistent:jl}),e}var Xn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},wl=le(Xn),yi=v({},Xn,{view:0,detail:0}),e0=le(yi),bu,Au,pi,Ll=v({},yi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Eu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==pi&&(pi&&t.type==="mousemove"?(bu=t.screenX-pi.screenX,Au=t.screenY-pi.screenY):Au=bu=0,pi=t),bu)},movementY:function(t){return"movementY"in t?t.movementY:Au}}),ff=le(Ll),n0=v({},Ll,{dataTransfer:0}),a0=le(n0),i0=v({},yi,{relatedTarget:0}),xu=le(i0),l0=v({},Xn,{animationName:0,elapsedTime:0,pseudoElement:0}),s0=le(l0),u0=v({},Xn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),o0=le(u0),r0=v({},Xn,{data:0}),hf=le(r0),c0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},f0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},h0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function d0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=h0[t])?!!e[t]:!1}function Eu(){return d0}var m0=v({},yi,{key:function(t){if(t.key){var e=c0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Bl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?f0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Eu,charCode:function(t){return t.type==="keypress"?Bl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Bl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),y0=le(m0),p0=v({},Ll,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),df=le(p0),g0=v({},yi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Eu}),v0=le(g0),S0=v({},Xn,{propertyName:0,elapsedTime:0,pseudoElement:0}),T0=le(S0),b0=v({},Ll,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),A0=le(b0),x0=v({},Xn,{newState:0,oldState:0}),E0=le(x0),M0=[9,13,27,32],Mu=Je&&"CompositionEvent"in window,gi=null;Je&&"documentMode"in document&&(gi=document.documentMode);var D0=Je&&"TextEvent"in window&&!gi,mf=Je&&(!Mu||gi&&8<gi&&11>=gi),yf=" ",pf=!1;function gf(t,e){switch(t){case"keyup":return M0.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Aa=!1;function R0(t,e){switch(t){case"compositionend":return vf(e);case"keypress":return e.which!==32?null:(pf=!0,yf);case"textInput":return t=e.data,t===yf&&pf?null:t;default:return null}}function O0(t,e){if(Aa)return t==="compositionend"||!Mu&&gf(t,e)?(t=rf(),Nl=Tu=yn=null,Aa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return mf&&e.locale!=="ko"?null:e.data;default:return null}}var C0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Sf(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!C0[t.type]:e==="textarea"}function Tf(t,e,n,a){Ta?ba?ba.push(a):ba=[a]:Ta=a,e=Es(e,"onChange"),0<e.length&&(n=new wl("onChange","change",null,n,a),t.push({event:n,listeners:e}))}var vi=null,Si=null;function V0(t){tm(t,0)}function Hl(t){var e=hi(t);if(tf(e))return t}function bf(t,e){if(t==="change")return e}var Af=!1;if(Je){var Du;if(Je){var Ru="oninput"in document;if(!Ru){var xf=document.createElement("div");xf.setAttribute("oninput","return;"),Ru=typeof xf.oninput=="function"}Du=Ru}else Du=!1;Af=Du&&(!document.documentMode||9<document.documentMode)}function Ef(){vi&&(vi.detachEvent("onpropertychange",Mf),Si=vi=null)}function Mf(t){if(t.propertyName==="value"&&Hl(Si)){var e=[];Tf(e,Si,t,gu(t)),of(V0,e)}}function z0(t,e,n){t==="focusin"?(Ef(),vi=e,Si=n,vi.attachEvent("onpropertychange",Mf)):t==="focusout"&&Ef()}function _0(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Hl(Si)}function U0(t,e){if(t==="click")return Hl(e)}function N0(t,e){if(t==="input"||t==="change")return Hl(e)}function B0(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var de=typeof Object.is=="function"?Object.is:B0;function Ti(t,e){if(de(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),a=Object.keys(e);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var s=n[a];if(!nu.call(e,s)||!de(t[s],e[s]))return!1}return!0}function Df(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Rf(t,e){var n=Df(t);t=0;for(var a;n;){if(n.nodeType===3){if(a=t+n.textContent.length,t<=e&&a>=e)return{node:n,offset:e-t};t=a}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Df(n)}}function Of(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Of(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Cf(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=_l(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=_l(t.document)}return e}function Ou(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var j0=Je&&"documentMode"in document&&11>=document.documentMode,xa=null,Cu=null,bi=null,Vu=!1;function Vf(t,e,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Vu||xa==null||xa!==_l(a)||(a=xa,"selectionStart"in a&&Ou(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),bi&&Ti(bi,a)||(bi=a,a=Es(Cu,"onSelect"),0<a.length&&(e=new wl("onSelect","select",null,e,n),t.push({event:e,listeners:a}),e.target=xa)))}function Zn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Ea={animationend:Zn("Animation","AnimationEnd"),animationiteration:Zn("Animation","AnimationIteration"),animationstart:Zn("Animation","AnimationStart"),transitionrun:Zn("Transition","TransitionRun"),transitionstart:Zn("Transition","TransitionStart"),transitioncancel:Zn("Transition","TransitionCancel"),transitionend:Zn("Transition","TransitionEnd")},zu={},zf={};Je&&(zf=document.createElement("div").style,"AnimationEvent"in window||(delete Ea.animationend.animation,delete Ea.animationiteration.animation,delete Ea.animationstart.animation),"TransitionEvent"in window||delete Ea.transitionend.transition);function Kn(t){if(zu[t])return zu[t];if(!Ea[t])return t;var e=Ea[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in zf)return zu[t]=e[n];return t}var _f=Kn("animationend"),Uf=Kn("animationiteration"),Nf=Kn("animationstart"),w0=Kn("transitionrun"),L0=Kn("transitionstart"),H0=Kn("transitioncancel"),Bf=Kn("transitionend"),jf=new Map,_u="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");_u.push("scrollEnd");function _e(t,e){jf.set(t,e),Gn(e,[t])}var wf=new WeakMap;function Ae(t,e){if(typeof t=="object"&&t!==null){var n=wf.get(t);return n!==void 0?n:(e={value:t,source:e,stack:$c(e)},wf.set(t,e),e)}return{value:t,source:e,stack:$c(e)}}var xe=[],Ma=0,Uu=0;function ql(){for(var t=Ma,e=Uu=Ma=0;e<t;){var n=xe[e];xe[e++]=null;var a=xe[e];xe[e++]=null;var s=xe[e];xe[e++]=null;var r=xe[e];if(xe[e++]=null,a!==null&&s!==null){var f=a.pending;f===null?s.next=s:(s.next=f.next,f.next=s),a.pending=s}r!==0&&Lf(n,s,r)}}function Yl(t,e,n,a){xe[Ma++]=t,xe[Ma++]=e,xe[Ma++]=n,xe[Ma++]=a,Uu|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Nu(t,e,n,a){return Yl(t,e,n,a),Gl(t)}function Da(t,e){return Yl(t,null,null,e),Gl(t)}function Lf(t,e,n){t.lanes|=n;var a=t.alternate;a!==null&&(a.lanes|=n);for(var s=!1,r=t.return;r!==null;)r.childLanes|=n,a=r.alternate,a!==null&&(a.childLanes|=n),r.tag===22&&(t=r.stateNode,t===null||t._visibility&1||(s=!0)),t=r,r=r.return;return t.tag===3?(r=t.stateNode,s&&e!==null&&(s=31-he(n),t=r.hiddenUpdates,a=t[s],a===null?t[s]=[e]:a.push(e),e.lane=n|536870912),r):null}function Gl(t){if(50<Qi)throw Qi=0,Yo=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Ra={};function q0(t,e,n,a){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function me(t,e,n,a){return new q0(t,e,n,a)}function Bu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Pe(t,e){var n=t.alternate;return n===null?(n=me(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function Hf(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Xl(t,e,n,a,s,r){var f=0;if(a=t,typeof t=="function")Bu(t)&&(f=1);else if(typeof t=="string")f=Gv(t,n,$.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case xt:return t=me(31,n,e,s),t.elementType=xt,t.lanes=r,t;case w:return Qn(n.children,s,r,e);case L:f=8,s|=24;break;case Y:return t=me(12,n,e,s|2),t.elementType=Y,t.lanes=r,t;case B:return t=me(13,n,e,s),t.elementType=B,t.lanes=r,t;case tt:return t=me(19,n,e,s),t.elementType=tt,t.lanes=r,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case q:case H:f=10;break t;case X:f=9;break t;case Q:f=11;break t;case it:f=14;break t;case F:f=16,a=null;break t}f=29,n=Error(o(130,t===null?"null":typeof t,"")),a=null}return e=me(f,n,e,s),e.elementType=t,e.type=a,e.lanes=r,e}function Qn(t,e,n,a){return t=me(7,t,a,e),t.lanes=n,t}function ju(t,e,n){return t=me(6,t,null,e),t.lanes=n,t}function wu(t,e,n){return e=me(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Oa=[],Ca=0,Zl=null,Kl=0,Ee=[],Me=0,kn=null,Fe=1,We="";function Jn(t,e){Oa[Ca++]=Kl,Oa[Ca++]=Zl,Zl=t,Kl=e}function qf(t,e,n){Ee[Me++]=Fe,Ee[Me++]=We,Ee[Me++]=kn,kn=t;var a=Fe;t=We;var s=32-he(a)-1;a&=~(1<<s),n+=1;var r=32-he(e)+s;if(30<r){var f=s-s%5;r=(a&(1<<f)-1).toString(32),a>>=f,s-=f,Fe=1<<32-he(e)+s|n<<s|a,We=r+t}else Fe=1<<r|n<<s|a,We=t}function Lu(t){t.return!==null&&(Jn(t,1),qf(t,1,0))}function Hu(t){for(;t===Zl;)Zl=Oa[--Ca],Oa[Ca]=null,Kl=Oa[--Ca],Oa[Ca]=null;for(;t===kn;)kn=Ee[--Me],Ee[Me]=null,We=Ee[--Me],Ee[Me]=null,Fe=Ee[--Me],Ee[Me]=null}var ne=null,Ct=null,yt=!1,Pn=null,Le=!1,qu=Error(o(519));function Fn(t){var e=Error(o(418,""));throw Ei(Ae(e,t)),qu}function Yf(t){var e=t.stateNode,n=t.type,a=t.memoizedProps;switch(e[$t]=t,e[ie]=a,n){case"dialog":ct("cancel",e),ct("close",e);break;case"iframe":case"object":case"embed":ct("load",e);break;case"video":case"audio":for(n=0;n<Ji.length;n++)ct(Ji[n],e);break;case"source":ct("error",e);break;case"img":case"image":case"link":ct("error",e),ct("load",e);break;case"details":ct("toggle",e);break;case"input":ct("invalid",e),ef(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),zl(e);break;case"select":ct("invalid",e);break;case"textarea":ct("invalid",e),af(e,a.value,a.defaultValue,a.children),zl(e)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||a.suppressHydrationWarning===!0||im(e.textContent,n)?(a.popover!=null&&(ct("beforetoggle",e),ct("toggle",e)),a.onScroll!=null&&ct("scroll",e),a.onScrollEnd!=null&&ct("scrollend",e),a.onClick!=null&&(e.onclick=Ms),e=!0):e=!1,e||Fn(t)}function Gf(t){for(ne=t.return;ne;)switch(ne.tag){case 5:case 13:Le=!1;return;case 27:case 3:Le=!0;return;default:ne=ne.return}}function Ai(t){if(t!==ne)return!1;if(!yt)return Gf(t),yt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||ar(t.type,t.memoizedProps)),n=!n),n&&Ct&&Fn(t),Gf(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Ct=Ne(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Ct=null}}else e===27?(e=Ct,zn(t.type)?(t=ur,ur=null,Ct=t):Ct=e):Ct=ne?Ne(t.stateNode.nextSibling):null;return!0}function xi(){Ct=ne=null,yt=!1}function Xf(){var t=Pn;return t!==null&&(oe===null?oe=t:oe.push.apply(oe,t),Pn=null),t}function Ei(t){Pn===null?Pn=[t]:Pn.push(t)}var Yu=N(null),Wn=null,$e=null;function pn(t,e,n){G(Yu,e._currentValue),e._currentValue=n}function Ie(t){t._currentValue=Yu.current,Z(Yu)}function Gu(t,e,n){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===n)break;t=t.return}}function Xu(t,e,n,a){var s=t.child;for(s!==null&&(s.return=t);s!==null;){var r=s.dependencies;if(r!==null){var f=s.child;r=r.firstContext;t:for(;r!==null;){var m=r;r=s;for(var S=0;S<e.length;S++)if(m.context===e[S]){r.lanes|=n,m=r.alternate,m!==null&&(m.lanes|=n),Gu(r.return,n,t),a||(f=null);break t}r=m.next}}else if(s.tag===18){if(f=s.return,f===null)throw Error(o(341));f.lanes|=n,r=f.alternate,r!==null&&(r.lanes|=n),Gu(f,n,t),f=null}else f=s.child;if(f!==null)f.return=s;else for(f=s;f!==null;){if(f===t){f=null;break}if(s=f.sibling,s!==null){s.return=f.return,f=s;break}f=f.return}s=f}}function Mi(t,e,n,a){t=null;for(var s=e,r=!1;s!==null;){if(!r){if((s.flags&524288)!==0)r=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var f=s.alternate;if(f===null)throw Error(o(387));if(f=f.memoizedProps,f!==null){var m=s.type;de(s.pendingProps.value,f.value)||(t!==null?t.push(m):t=[m])}}else if(s===ce.current){if(f=s.alternate,f===null)throw Error(o(387));f.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(t!==null?t.push(tl):t=[tl])}s=s.return}t!==null&&Xu(e,t,n,a),e.flags|=262144}function Ql(t){for(t=t.firstContext;t!==null;){if(!de(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function $n(t){Wn=t,$e=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function It(t){return Zf(Wn,t)}function kl(t,e){return Wn===null&&$n(t),Zf(t,e)}function Zf(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},$e===null){if(t===null)throw Error(o(308));$e=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else $e=$e.next=e;return n}var Y0=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},G0=i.unstable_scheduleCallback,X0=i.unstable_NormalPriority,Yt={$$typeof:H,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Zu(){return{controller:new Y0,data:new Map,refCount:0}}function Di(t){t.refCount--,t.refCount===0&&G0(X0,function(){t.controller.abort()})}var Ri=null,Ku=0,Va=0,za=null;function Z0(t,e){if(Ri===null){var n=Ri=[];Ku=0,Va=Jo(),za={status:"pending",value:void 0,then:function(a){n.push(a)}}}return Ku++,e.then(Kf,Kf),e}function Kf(){if(--Ku===0&&Ri!==null){za!==null&&(za.status="fulfilled");var t=Ri;Ri=null,Va=0,za=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function K0(t,e){var n=[],a={status:"pending",value:null,reason:null,then:function(s){n.push(s)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var s=0;s<n.length;s++)(0,n[s])(e)},function(s){for(a.status="rejected",a.reason=s,s=0;s<n.length;s++)(0,n[s])(void 0)}),a}var Qf=V.S;V.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Z0(t,e),Qf!==null&&Qf(t,e)};var In=N(null);function Qu(){var t=In.current;return t!==null?t:At.pooledCache}function Jl(t,e){e===null?G(In,In.current):G(In,e.pool)}function kf(){var t=Qu();return t===null?null:{parent:Yt._currentValue,pool:t}}var Oi=Error(o(460)),Jf=Error(o(474)),Pl=Error(o(542)),ku={then:function(){}};function Pf(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Fl(){}function Ff(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Fl,Fl),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,$f(t),t;default:if(typeof e.status=="string")e.then(Fl,Fl);else{if(t=At,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var s=e;s.status="fulfilled",s.value=a}},function(a){if(e.status==="pending"){var s=e;s.status="rejected",s.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,$f(t),t}throw Ci=e,Oi}}var Ci=null;function Wf(){if(Ci===null)throw Error(o(459));var t=Ci;return Ci=null,t}function $f(t){if(t===Oi||t===Pl)throw Error(o(483))}var gn=!1;function Ju(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Pu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function vn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Sn(t,e,n){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(pt&2)!==0){var s=a.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),a.pending=e,e=Gl(t),Lf(t,null,n),e}return Yl(t,a,e,n),Gl(t)}function Vi(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,n|=a,e.lanes=n,Zc(t,n)}}function Fu(t,e){var n=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var s=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?s=r=f:r=r.next=f,n=n.next}while(n!==null);r===null?s=r=e:r=r.next=e}else s=r=e;n={baseState:a.baseState,firstBaseUpdate:s,lastBaseUpdate:r,shared:a.shared,callbacks:a.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Wu=!1;function zi(){if(Wu){var t=za;if(t!==null)throw t}}function _i(t,e,n,a){Wu=!1;var s=t.updateQueue;gn=!1;var r=s.firstBaseUpdate,f=s.lastBaseUpdate,m=s.shared.pending;if(m!==null){s.shared.pending=null;var S=m,D=S.next;S.next=null,f===null?r=D:f.next=D,f=S;var C=t.alternate;C!==null&&(C=C.updateQueue,m=C.lastBaseUpdate,m!==f&&(m===null?C.firstBaseUpdate=D:m.next=D,C.lastBaseUpdate=S))}if(r!==null){var _=s.baseState;f=0,C=D=S=null,m=r;do{var R=m.lane&-536870913,O=R!==m.lane;if(O?(ht&R)===R:(a&R)===R){R!==0&&R===Va&&(Wu=!0),C!==null&&(C=C.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});t:{var nt=t,I=m;R=e;var Tt=n;switch(I.tag){case 1:if(nt=I.payload,typeof nt=="function"){_=nt.call(Tt,_,R);break t}_=nt;break t;case 3:nt.flags=nt.flags&-65537|128;case 0:if(nt=I.payload,R=typeof nt=="function"?nt.call(Tt,_,R):nt,R==null)break t;_=v({},_,R);break t;case 2:gn=!0}}R=m.callback,R!==null&&(t.flags|=64,O&&(t.flags|=8192),O=s.callbacks,O===null?s.callbacks=[R]:O.push(R))}else O={lane:R,tag:m.tag,payload:m.payload,callback:m.callback,next:null},C===null?(D=C=O,S=_):C=C.next=O,f|=R;if(m=m.next,m===null){if(m=s.shared.pending,m===null)break;O=m,m=O.next,O.next=null,s.lastBaseUpdate=O,s.shared.pending=null}}while(!0);C===null&&(S=_),s.baseState=S,s.firstBaseUpdate=D,s.lastBaseUpdate=C,r===null&&(s.shared.lanes=0),Rn|=f,t.lanes=f,t.memoizedState=_}}function If(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function th(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)If(n[t],e)}var _a=N(null),Wl=N(0);function eh(t,e){t=un,G(Wl,t),G(_a,e),un=t|e.baseLanes}function $u(){G(Wl,un),G(_a,_a.current)}function Iu(){un=Wl.current,Z(_a),Z(Wl)}var Tn=0,ut=null,vt=null,Bt=null,$l=!1,Ua=!1,ta=!1,Il=0,Ui=0,Na=null,Q0=0;function _t(){throw Error(o(321))}function to(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!de(t[n],e[n]))return!1;return!0}function eo(t,e,n,a,s,r){return Tn=r,ut=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,V.H=t===null||t.memoizedState===null?Lh:Hh,ta=!1,r=n(a,s),ta=!1,Ua&&(r=ah(e,n,a,s)),nh(t),r}function nh(t){V.H=ls;var e=vt!==null&&vt.next!==null;if(Tn=0,Bt=vt=ut=null,$l=!1,Ui=0,Na=null,e)throw Error(o(300));t===null||Zt||(t=t.dependencies,t!==null&&Ql(t)&&(Zt=!0))}function ah(t,e,n,a){ut=t;var s=0;do{if(Ua&&(Na=null),Ui=0,Ua=!1,25<=s)throw Error(o(301));if(s+=1,Bt=vt=null,t.updateQueue!=null){var r=t.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}V.H=I0,r=e(n,a)}while(Ua);return r}function k0(){var t=V.H,e=t.useState()[0];return e=typeof e.then=="function"?Ni(e):e,t=t.useState()[0],(vt!==null?vt.memoizedState:null)!==t&&(ut.flags|=1024),e}function no(){var t=Il!==0;return Il=0,t}function ao(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function io(t){if($l){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}$l=!1}Tn=0,Bt=vt=ut=null,Ua=!1,Ui=Il=0,Na=null}function se(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Bt===null?ut.memoizedState=Bt=t:Bt=Bt.next=t,Bt}function jt(){if(vt===null){var t=ut.alternate;t=t!==null?t.memoizedState:null}else t=vt.next;var e=Bt===null?ut.memoizedState:Bt.next;if(e!==null)Bt=e,vt=t;else{if(t===null)throw ut.alternate===null?Error(o(467)):Error(o(310));vt=t,t={memoizedState:vt.memoizedState,baseState:vt.baseState,baseQueue:vt.baseQueue,queue:vt.queue,next:null},Bt===null?ut.memoizedState=Bt=t:Bt=Bt.next=t}return Bt}function lo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ni(t){var e=Ui;return Ui+=1,Na===null&&(Na=[]),t=Ff(Na,t,e),e=ut,(Bt===null?e.memoizedState:Bt.next)===null&&(e=e.alternate,V.H=e===null||e.memoizedState===null?Lh:Hh),t}function ts(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Ni(t);if(t.$$typeof===H)return It(t)}throw Error(o(438,String(t)))}function so(t){var e=null,n=ut.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var a=ut.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(s){return s.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=lo(),ut.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),a=0;a<t;a++)n[a]=Lt;return e.index++,n}function tn(t,e){return typeof e=="function"?e(t):e}function es(t){var e=jt();return uo(e,vt,t)}function uo(t,e,n){var a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=n;var s=t.baseQueue,r=a.pending;if(r!==null){if(s!==null){var f=s.next;s.next=r.next,r.next=f}e.baseQueue=s=r,a.pending=null}if(r=t.baseState,s===null)t.memoizedState=r;else{e=s.next;var m=f=null,S=null,D=e,C=!1;do{var _=D.lane&-536870913;if(_!==D.lane?(ht&_)===_:(Tn&_)===_){var R=D.revertLane;if(R===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),_===Va&&(C=!0);else if((Tn&R)===R){D=D.next,R===Va&&(C=!0);continue}else _={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},S===null?(m=S=_,f=r):S=S.next=_,ut.lanes|=R,Rn|=R;_=D.action,ta&&n(r,_),r=D.hasEagerState?D.eagerState:n(r,_)}else R={lane:_,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},S===null?(m=S=R,f=r):S=S.next=R,ut.lanes|=_,Rn|=_;D=D.next}while(D!==null&&D!==e);if(S===null?f=r:S.next=m,!de(r,t.memoizedState)&&(Zt=!0,C&&(n=za,n!==null)))throw n;t.memoizedState=r,t.baseState=f,t.baseQueue=S,a.lastRenderedState=r}return s===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function oo(t){var e=jt(),n=e.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=t;var a=n.dispatch,s=n.pending,r=e.memoizedState;if(s!==null){n.pending=null;var f=s=s.next;do r=t(r,f.action),f=f.next;while(f!==s);de(r,e.memoizedState)||(Zt=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,a]}function ih(t,e,n){var a=ut,s=jt(),r=yt;if(r){if(n===void 0)throw Error(o(407));n=n()}else n=e();var f=!de((vt||s).memoizedState,n);f&&(s.memoizedState=n,Zt=!0),s=s.queue;var m=uh.bind(null,a,s,t);if(Bi(2048,8,m,[t]),s.getSnapshot!==e||f||Bt!==null&&Bt.memoizedState.tag&1){if(a.flags|=2048,Ba(9,ns(),sh.bind(null,a,s,n,e),null),At===null)throw Error(o(349));r||(Tn&124)!==0||lh(a,e,n)}return n}function lh(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ut.updateQueue,e===null?(e=lo(),ut.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function sh(t,e,n,a){e.value=n,e.getSnapshot=a,oh(e)&&rh(t)}function uh(t,e,n){return n(function(){oh(e)&&rh(t)})}function oh(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!de(t,n)}catch{return!0}}function rh(t){var e=Da(t,2);e!==null&&Se(e,t,2)}function ro(t){var e=se();if(typeof t=="function"){var n=t;if(t=n(),ta){dn(!0);try{n()}finally{dn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:tn,lastRenderedState:t},e}function ch(t,e,n,a){return t.baseState=n,uo(t,vt,typeof a=="function"?a:tn)}function J0(t,e,n,a,s){if(is(t))throw Error(o(485));if(t=e.action,t!==null){var r={payload:s,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){r.listeners.push(f)}};V.T!==null?n(!0):r.isTransition=!1,a(r),n=e.pending,n===null?(r.next=e.pending=r,fh(e,r)):(r.next=n.next,e.pending=n.next=r)}}function fh(t,e){var n=e.action,a=e.payload,s=t.state;if(e.isTransition){var r=V.T,f={};V.T=f;try{var m=n(s,a),S=V.S;S!==null&&S(f,m),hh(t,e,m)}catch(D){co(t,e,D)}finally{V.T=r}}else try{r=n(s,a),hh(t,e,r)}catch(D){co(t,e,D)}}function hh(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(a){dh(t,e,a)},function(a){return co(t,e,a)}):dh(t,e,n)}function dh(t,e,n){e.status="fulfilled",e.value=n,mh(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,fh(t,n)))}function co(t,e,n){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=n,mh(e),e=e.next;while(e!==a)}t.action=null}function mh(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function yh(t,e){return e}function ph(t,e){if(yt){var n=At.formState;if(n!==null){t:{var a=ut;if(yt){if(Ct){e:{for(var s=Ct,r=Le;s.nodeType!==8;){if(!r){s=null;break e}if(s=Ne(s.nextSibling),s===null){s=null;break e}}r=s.data,s=r==="F!"||r==="F"?s:null}if(s){Ct=Ne(s.nextSibling),a=s.data==="F!";break t}}Fn(a)}a=!1}a&&(e=n[0])}}return n=se(),n.memoizedState=n.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:yh,lastRenderedState:e},n.queue=a,n=Bh.bind(null,ut,a),a.dispatch=n,a=ro(!1),r=po.bind(null,ut,!1,a.queue),a=se(),s={state:e,dispatch:null,action:t,pending:null},a.queue=s,n=J0.bind(null,ut,s,r,n),s.dispatch=n,a.memoizedState=t,[e,n,!1]}function gh(t){var e=jt();return vh(e,vt,t)}function vh(t,e,n){if(e=uo(t,e,yh)[0],t=es(tn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=Ni(e)}catch(f){throw f===Oi?Pl:f}else a=e;e=jt();var s=e.queue,r=s.dispatch;return n!==e.memoizedState&&(ut.flags|=2048,Ba(9,ns(),P0.bind(null,s,n),null)),[a,r,t]}function P0(t,e){t.action=e}function Sh(t){var e=jt(),n=vt;if(n!==null)return vh(e,n,t);jt(),e=e.memoizedState,n=jt();var a=n.queue.dispatch;return n.memoizedState=t,[e,a,!1]}function Ba(t,e,n,a){return t={tag:t,create:n,deps:a,inst:e,next:null},e=ut.updateQueue,e===null&&(e=lo(),ut.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(a=n.next,n.next=t,t.next=a,e.lastEffect=t),t}function ns(){return{destroy:void 0,resource:void 0}}function Th(){return jt().memoizedState}function as(t,e,n,a){var s=se();a=a===void 0?null:a,ut.flags|=t,s.memoizedState=Ba(1|e,ns(),n,a)}function Bi(t,e,n,a){var s=jt();a=a===void 0?null:a;var r=s.memoizedState.inst;vt!==null&&a!==null&&to(a,vt.memoizedState.deps)?s.memoizedState=Ba(e,r,n,a):(ut.flags|=t,s.memoizedState=Ba(1|e,r,n,a))}function bh(t,e){as(8390656,8,t,e)}function Ah(t,e){Bi(2048,8,t,e)}function xh(t,e){return Bi(4,2,t,e)}function Eh(t,e){return Bi(4,4,t,e)}function Mh(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Dh(t,e,n){n=n!=null?n.concat([t]):null,Bi(4,4,Mh.bind(null,e,t),n)}function fo(){}function Rh(t,e){var n=jt();e=e===void 0?null:e;var a=n.memoizedState;return e!==null&&to(e,a[1])?a[0]:(n.memoizedState=[t,e],t)}function Oh(t,e){var n=jt();e=e===void 0?null:e;var a=n.memoizedState;if(e!==null&&to(e,a[1]))return a[0];if(a=t(),ta){dn(!0);try{t()}finally{dn(!1)}}return n.memoizedState=[a,e],a}function ho(t,e,n){return n===void 0||(Tn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=zd(),ut.lanes|=t,Rn|=t,n)}function Ch(t,e,n,a){return de(n,e)?n:_a.current!==null?(t=ho(t,n,a),de(t,e)||(Zt=!0),t):(Tn&42)===0?(Zt=!0,t.memoizedState=n):(t=zd(),ut.lanes|=t,Rn|=t,e)}function Vh(t,e,n,a,s){var r=j.p;j.p=r!==0&&8>r?r:8;var f=V.T,m={};V.T=m,po(t,!1,e,n);try{var S=s(),D=V.S;if(D!==null&&D(m,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var C=K0(S,a);ji(t,e,C,ve(t))}else ji(t,e,a,ve(t))}catch(_){ji(t,e,{then:function(){},status:"rejected",reason:_},ve())}finally{j.p=r,V.T=f}}function F0(){}function mo(t,e,n,a){if(t.tag!==5)throw Error(o(476));var s=zh(t).queue;Vh(t,s,e,k,n===null?F0:function(){return _h(t),n(a)})}function zh(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:k,baseState:k,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:tn,lastRenderedState:k},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:tn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function _h(t){var e=zh(t).next.queue;ji(t,e,{},ve())}function yo(){return It(tl)}function Uh(){return jt().memoizedState}function Nh(){return jt().memoizedState}function W0(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ve();t=vn(n);var a=Sn(e,t,n);a!==null&&(Se(a,e,n),Vi(a,e,n)),e={cache:Zu()},t.payload=e;return}e=e.return}}function $0(t,e,n){var a=ve();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},is(t)?jh(e,n):(n=Nu(t,e,n,a),n!==null&&(Se(n,t,a),wh(n,e,a)))}function Bh(t,e,n){var a=ve();ji(t,e,n,a)}function ji(t,e,n,a){var s={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(is(t))jh(e,s);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var f=e.lastRenderedState,m=r(f,n);if(s.hasEagerState=!0,s.eagerState=m,de(m,f))return Yl(t,e,s,0),At===null&&ql(),!1}catch{}finally{}if(n=Nu(t,e,s,a),n!==null)return Se(n,t,a),wh(n,e,a),!0}return!1}function po(t,e,n,a){if(a={lane:2,revertLane:Jo(),action:a,hasEagerState:!1,eagerState:null,next:null},is(t)){if(e)throw Error(o(479))}else e=Nu(t,n,a,2),e!==null&&Se(e,t,2)}function is(t){var e=t.alternate;return t===ut||e!==null&&e===ut}function jh(t,e){Ua=$l=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function wh(t,e,n){if((n&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,n|=a,e.lanes=n,Zc(t,n)}}var ls={readContext:It,use:ts,useCallback:_t,useContext:_t,useEffect:_t,useImperativeHandle:_t,useLayoutEffect:_t,useInsertionEffect:_t,useMemo:_t,useReducer:_t,useRef:_t,useState:_t,useDebugValue:_t,useDeferredValue:_t,useTransition:_t,useSyncExternalStore:_t,useId:_t,useHostTransitionStatus:_t,useFormState:_t,useActionState:_t,useOptimistic:_t,useMemoCache:_t,useCacheRefresh:_t},Lh={readContext:It,use:ts,useCallback:function(t,e){return se().memoizedState=[t,e===void 0?null:e],t},useContext:It,useEffect:bh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,as(4194308,4,Mh.bind(null,e,t),n)},useLayoutEffect:function(t,e){return as(4194308,4,t,e)},useInsertionEffect:function(t,e){as(4,2,t,e)},useMemo:function(t,e){var n=se();e=e===void 0?null:e;var a=t();if(ta){dn(!0);try{t()}finally{dn(!1)}}return n.memoizedState=[a,e],a},useReducer:function(t,e,n){var a=se();if(n!==void 0){var s=n(e);if(ta){dn(!0);try{n(e)}finally{dn(!1)}}}else s=e;return a.memoizedState=a.baseState=s,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:s},a.queue=t,t=t.dispatch=$0.bind(null,ut,t),[a.memoizedState,t]},useRef:function(t){var e=se();return t={current:t},e.memoizedState=t},useState:function(t){t=ro(t);var e=t.queue,n=Bh.bind(null,ut,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:fo,useDeferredValue:function(t,e){var n=se();return ho(n,t,e)},useTransition:function(){var t=ro(!1);return t=Vh.bind(null,ut,t.queue,!0,!1),se().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var a=ut,s=se();if(yt){if(n===void 0)throw Error(o(407));n=n()}else{if(n=e(),At===null)throw Error(o(349));(ht&124)!==0||lh(a,e,n)}s.memoizedState=n;var r={value:n,getSnapshot:e};return s.queue=r,bh(uh.bind(null,a,r,t),[t]),a.flags|=2048,Ba(9,ns(),sh.bind(null,a,r,n,e),null),n},useId:function(){var t=se(),e=At.identifierPrefix;if(yt){var n=We,a=Fe;n=(a&~(1<<32-he(a)-1)).toString(32)+n,e="«"+e+"R"+n,n=Il++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=Q0++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:yo,useFormState:ph,useActionState:ph,useOptimistic:function(t){var e=se();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=po.bind(null,ut,!0,n),n.dispatch=e,[t,e]},useMemoCache:so,useCacheRefresh:function(){return se().memoizedState=W0.bind(null,ut)}},Hh={readContext:It,use:ts,useCallback:Rh,useContext:It,useEffect:Ah,useImperativeHandle:Dh,useInsertionEffect:xh,useLayoutEffect:Eh,useMemo:Oh,useReducer:es,useRef:Th,useState:function(){return es(tn)},useDebugValue:fo,useDeferredValue:function(t,e){var n=jt();return Ch(n,vt.memoizedState,t,e)},useTransition:function(){var t=es(tn)[0],e=jt().memoizedState;return[typeof t=="boolean"?t:Ni(t),e]},useSyncExternalStore:ih,useId:Uh,useHostTransitionStatus:yo,useFormState:gh,useActionState:gh,useOptimistic:function(t,e){var n=jt();return ch(n,vt,t,e)},useMemoCache:so,useCacheRefresh:Nh},I0={readContext:It,use:ts,useCallback:Rh,useContext:It,useEffect:Ah,useImperativeHandle:Dh,useInsertionEffect:xh,useLayoutEffect:Eh,useMemo:Oh,useReducer:oo,useRef:Th,useState:function(){return oo(tn)},useDebugValue:fo,useDeferredValue:function(t,e){var n=jt();return vt===null?ho(n,t,e):Ch(n,vt.memoizedState,t,e)},useTransition:function(){var t=oo(tn)[0],e=jt().memoizedState;return[typeof t=="boolean"?t:Ni(t),e]},useSyncExternalStore:ih,useId:Uh,useHostTransitionStatus:yo,useFormState:Sh,useActionState:Sh,useOptimistic:function(t,e){var n=jt();return vt!==null?ch(n,vt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:so,useCacheRefresh:Nh},ja=null,wi=0;function ss(t){var e=wi;return wi+=1,ja===null&&(ja=[]),Ff(ja,t,e)}function Li(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function us(t,e){throw e.$$typeof===A?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function qh(t){var e=t._init;return e(t._payload)}function Yh(t){function e(E,b){if(t){var M=E.deletions;M===null?(E.deletions=[b],E.flags|=16):M.push(b)}}function n(E,b){if(!t)return null;for(;b!==null;)e(E,b),b=b.sibling;return null}function a(E){for(var b=new Map;E!==null;)E.key!==null?b.set(E.key,E):b.set(E.index,E),E=E.sibling;return b}function s(E,b){return E=Pe(E,b),E.index=0,E.sibling=null,E}function r(E,b,M){return E.index=M,t?(M=E.alternate,M!==null?(M=M.index,M<b?(E.flags|=67108866,b):M):(E.flags|=67108866,b)):(E.flags|=1048576,b)}function f(E){return t&&E.alternate===null&&(E.flags|=67108866),E}function m(E,b,M,z){return b===null||b.tag!==6?(b=ju(M,E.mode,z),b.return=E,b):(b=s(b,M),b.return=E,b)}function S(E,b,M,z){var K=M.type;return K===w?C(E,b,M.props.children,z,M.key):b!==null&&(b.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===F&&qh(K)===b.type)?(b=s(b,M.props),Li(b,M),b.return=E,b):(b=Xl(M.type,M.key,M.props,null,E.mode,z),Li(b,M),b.return=E,b)}function D(E,b,M,z){return b===null||b.tag!==4||b.stateNode.containerInfo!==M.containerInfo||b.stateNode.implementation!==M.implementation?(b=wu(M,E.mode,z),b.return=E,b):(b=s(b,M.children||[]),b.return=E,b)}function C(E,b,M,z,K){return b===null||b.tag!==7?(b=Qn(M,E.mode,z,K),b.return=E,b):(b=s(b,M),b.return=E,b)}function _(E,b,M){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=ju(""+b,E.mode,M),b.return=E,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case x:return M=Xl(b.type,b.key,b.props,null,E.mode,M),Li(M,b),M.return=E,M;case U:return b=wu(b,E.mode,M),b.return=E,b;case F:var z=b._init;return b=z(b._payload),_(E,b,M)}if(qt(b)||Ht(b))return b=Qn(b,E.mode,M,null),b.return=E,b;if(typeof b.then=="function")return _(E,ss(b),M);if(b.$$typeof===H)return _(E,kl(E,b),M);us(E,b)}return null}function R(E,b,M,z){var K=b!==null?b.key:null;if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return K!==null?null:m(E,b,""+M,z);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case x:return M.key===K?S(E,b,M,z):null;case U:return M.key===K?D(E,b,M,z):null;case F:return K=M._init,M=K(M._payload),R(E,b,M,z)}if(qt(M)||Ht(M))return K!==null?null:C(E,b,M,z,null);if(typeof M.then=="function")return R(E,b,ss(M),z);if(M.$$typeof===H)return R(E,b,kl(E,M),z);us(E,M)}return null}function O(E,b,M,z,K){if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return E=E.get(M)||null,m(b,E,""+z,K);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case x:return E=E.get(z.key===null?M:z.key)||null,S(b,E,z,K);case U:return E=E.get(z.key===null?M:z.key)||null,D(b,E,z,K);case F:var ot=z._init;return z=ot(z._payload),O(E,b,M,z,K)}if(qt(z)||Ht(z))return E=E.get(M)||null,C(b,E,z,K,null);if(typeof z.then=="function")return O(E,b,M,ss(z),K);if(z.$$typeof===H)return O(E,b,M,kl(b,z),K);us(b,z)}return null}function nt(E,b,M,z){for(var K=null,ot=null,P=b,et=b=0,Qt=null;P!==null&&et<M.length;et++){P.index>et?(Qt=P,P=null):Qt=P.sibling;var mt=R(E,P,M[et],z);if(mt===null){P===null&&(P=Qt);break}t&&P&&mt.alternate===null&&e(E,P),b=r(mt,b,et),ot===null?K=mt:ot.sibling=mt,ot=mt,P=Qt}if(et===M.length)return n(E,P),yt&&Jn(E,et),K;if(P===null){for(;et<M.length;et++)P=_(E,M[et],z),P!==null&&(b=r(P,b,et),ot===null?K=P:ot.sibling=P,ot=P);return yt&&Jn(E,et),K}for(P=a(P);et<M.length;et++)Qt=O(P,E,et,M[et],z),Qt!==null&&(t&&Qt.alternate!==null&&P.delete(Qt.key===null?et:Qt.key),b=r(Qt,b,et),ot===null?K=Qt:ot.sibling=Qt,ot=Qt);return t&&P.forEach(function(jn){return e(E,jn)}),yt&&Jn(E,et),K}function I(E,b,M,z){if(M==null)throw Error(o(151));for(var K=null,ot=null,P=b,et=b=0,Qt=null,mt=M.next();P!==null&&!mt.done;et++,mt=M.next()){P.index>et?(Qt=P,P=null):Qt=P.sibling;var jn=R(E,P,mt.value,z);if(jn===null){P===null&&(P=Qt);break}t&&P&&jn.alternate===null&&e(E,P),b=r(jn,b,et),ot===null?K=jn:ot.sibling=jn,ot=jn,P=Qt}if(mt.done)return n(E,P),yt&&Jn(E,et),K;if(P===null){for(;!mt.done;et++,mt=M.next())mt=_(E,mt.value,z),mt!==null&&(b=r(mt,b,et),ot===null?K=mt:ot.sibling=mt,ot=mt);return yt&&Jn(E,et),K}for(P=a(P);!mt.done;et++,mt=M.next())mt=O(P,E,et,mt.value,z),mt!==null&&(t&&mt.alternate!==null&&P.delete(mt.key===null?et:mt.key),b=r(mt,b,et),ot===null?K=mt:ot.sibling=mt,ot=mt);return t&&P.forEach(function(t1){return e(E,t1)}),yt&&Jn(E,et),K}function Tt(E,b,M,z){if(typeof M=="object"&&M!==null&&M.type===w&&M.key===null&&(M=M.props.children),typeof M=="object"&&M!==null){switch(M.$$typeof){case x:t:{for(var K=M.key;b!==null;){if(b.key===K){if(K=M.type,K===w){if(b.tag===7){n(E,b.sibling),z=s(b,M.props.children),z.return=E,E=z;break t}}else if(b.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===F&&qh(K)===b.type){n(E,b.sibling),z=s(b,M.props),Li(z,M),z.return=E,E=z;break t}n(E,b);break}else e(E,b);b=b.sibling}M.type===w?(z=Qn(M.props.children,E.mode,z,M.key),z.return=E,E=z):(z=Xl(M.type,M.key,M.props,null,E.mode,z),Li(z,M),z.return=E,E=z)}return f(E);case U:t:{for(K=M.key;b!==null;){if(b.key===K)if(b.tag===4&&b.stateNode.containerInfo===M.containerInfo&&b.stateNode.implementation===M.implementation){n(E,b.sibling),z=s(b,M.children||[]),z.return=E,E=z;break t}else{n(E,b);break}else e(E,b);b=b.sibling}z=wu(M,E.mode,z),z.return=E,E=z}return f(E);case F:return K=M._init,M=K(M._payload),Tt(E,b,M,z)}if(qt(M))return nt(E,b,M,z);if(Ht(M)){if(K=Ht(M),typeof K!="function")throw Error(o(150));return M=K.call(M),I(E,b,M,z)}if(typeof M.then=="function")return Tt(E,b,ss(M),z);if(M.$$typeof===H)return Tt(E,b,kl(E,M),z);us(E,M)}return typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint"?(M=""+M,b!==null&&b.tag===6?(n(E,b.sibling),z=s(b,M),z.return=E,E=z):(n(E,b),z=ju(M,E.mode,z),z.return=E,E=z),f(E)):n(E,b)}return function(E,b,M,z){try{wi=0;var K=Tt(E,b,M,z);return ja=null,K}catch(P){if(P===Oi||P===Pl)throw P;var ot=me(29,P,null,E.mode);return ot.lanes=z,ot.return=E,ot}finally{}}}var wa=Yh(!0),Gh=Yh(!1),De=N(null),He=null;function bn(t){var e=t.alternate;G(Gt,Gt.current&1),G(De,t),He===null&&(e===null||_a.current!==null||e.memoizedState!==null)&&(He=t)}function Xh(t){if(t.tag===22){if(G(Gt,Gt.current),G(De,t),He===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(He=t)}}else An()}function An(){G(Gt,Gt.current),G(De,De.current)}function en(t){Z(De),He===t&&(He=null),Z(Gt)}var Gt=N(0);function os(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||sr(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function go(t,e,n,a){e=t.memoizedState,n=n(a,e),n=n==null?e:v({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var vo={enqueueSetState:function(t,e,n){t=t._reactInternals;var a=ve(),s=vn(a);s.payload=e,n!=null&&(s.callback=n),e=Sn(t,s,a),e!==null&&(Se(e,t,a),Vi(e,t,a))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var a=ve(),s=vn(a);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=Sn(t,s,a),e!==null&&(Se(e,t,a),Vi(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ve(),a=vn(n);a.tag=2,e!=null&&(a.callback=e),e=Sn(t,a,n),e!==null&&(Se(e,t,n),Vi(e,t,n))}};function Zh(t,e,n,a,s,r,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,r,f):e.prototype&&e.prototype.isPureReactComponent?!Ti(n,a)||!Ti(s,r):!0}function Kh(t,e,n,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,a),e.state!==t&&vo.enqueueReplaceState(e,e.state,null)}function ea(t,e){var n=e;if("ref"in e){n={};for(var a in e)a!=="ref"&&(n[a]=e[a])}if(t=t.defaultProps){n===e&&(n=v({},n));for(var s in t)n[s]===void 0&&(n[s]=t[s])}return n}var rs=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Qh(t){rs(t)}function kh(t){console.error(t)}function Jh(t){rs(t)}function cs(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function Ph(t,e,n){try{var a=t.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function So(t,e,n){return n=vn(n),n.tag=3,n.payload={element:null},n.callback=function(){cs(t,e)},n}function Fh(t){return t=vn(t),t.tag=3,t}function Wh(t,e,n,a){var s=n.type.getDerivedStateFromError;if(typeof s=="function"){var r=a.value;t.payload=function(){return s(r)},t.callback=function(){Ph(e,n,a)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){Ph(e,n,a),typeof s!="function"&&(On===null?On=new Set([this]):On.add(this));var m=a.stack;this.componentDidCatch(a.value,{componentStack:m!==null?m:""})})}function tv(t,e,n,a,s){if(n.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=n.alternate,e!==null&&Mi(e,n,s,!0),n=De.current,n!==null){switch(n.tag){case 13:return He===null?Xo():n.alternate===null&&Vt===0&&(Vt=3),n.flags&=-257,n.flags|=65536,n.lanes=s,a===ku?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([a]):e.add(a),Ko(t,a,s)),!1;case 22:return n.flags|=65536,a===ku?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([a]):n.add(a)),Ko(t,a,s)),!1}throw Error(o(435,n.tag))}return Ko(t,a,s),Xo(),!1}if(yt)return e=De.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=s,a!==qu&&(t=Error(o(422),{cause:a}),Ei(Ae(t,n)))):(a!==qu&&(e=Error(o(423),{cause:a}),Ei(Ae(e,n))),t=t.current.alternate,t.flags|=65536,s&=-s,t.lanes|=s,a=Ae(a,n),s=So(t.stateNode,a,s),Fu(t,s),Vt!==4&&(Vt=2)),!1;var r=Error(o(520),{cause:a});if(r=Ae(r,n),Ki===null?Ki=[r]:Ki.push(r),Vt!==4&&(Vt=2),e===null)return!0;a=Ae(a,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=s&-s,n.lanes|=t,t=So(n.stateNode,a,t),Fu(n,t),!1;case 1:if(e=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(On===null||!On.has(r))))return n.flags|=65536,s&=-s,n.lanes|=s,s=Fh(s),Wh(s,t,n,a),Fu(n,s),!1}n=n.return}while(n!==null);return!1}var $h=Error(o(461)),Zt=!1;function kt(t,e,n,a){e.child=t===null?Gh(e,null,n,a):wa(e,t.child,n,a)}function Ih(t,e,n,a,s){n=n.render;var r=e.ref;if("ref"in a){var f={};for(var m in a)m!=="ref"&&(f[m]=a[m])}else f=a;return $n(e),a=eo(t,e,n,f,r,s),m=no(),t!==null&&!Zt?(ao(t,e,s),nn(t,e,s)):(yt&&m&&Lu(e),e.flags|=1,kt(t,e,a,s),e.child)}function td(t,e,n,a,s){if(t===null){var r=n.type;return typeof r=="function"&&!Bu(r)&&r.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=r,ed(t,e,r,a,s)):(t=Xl(n.type,null,a,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!Ro(t,s)){var f=r.memoizedProps;if(n=n.compare,n=n!==null?n:Ti,n(f,a)&&t.ref===e.ref)return nn(t,e,s)}return e.flags|=1,t=Pe(r,a),t.ref=e.ref,t.return=e,e.child=t}function ed(t,e,n,a,s){if(t!==null){var r=t.memoizedProps;if(Ti(r,a)&&t.ref===e.ref)if(Zt=!1,e.pendingProps=a=r,Ro(t,s))(t.flags&131072)!==0&&(Zt=!0);else return e.lanes=t.lanes,nn(t,e,s)}return To(t,e,n,a,s)}function nd(t,e,n){var a=e.pendingProps,s=a.children,r=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=r!==null?r.baseLanes|n:n,t!==null){for(s=e.child=t.child,r=0;s!==null;)r=r|s.lanes|s.childLanes,s=s.sibling;e.childLanes=r&~a}else e.childLanes=0,e.child=null;return ad(t,e,a,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Jl(e,r!==null?r.cachePool:null),r!==null?eh(e,r):$u(),Xh(e);else return e.lanes=e.childLanes=536870912,ad(t,e,r!==null?r.baseLanes|n:n,n)}else r!==null?(Jl(e,r.cachePool),eh(e,r),An(),e.memoizedState=null):(t!==null&&Jl(e,null),$u(),An());return kt(t,e,s,n),e.child}function ad(t,e,n,a){var s=Qu();return s=s===null?null:{parent:Yt._currentValue,pool:s},e.memoizedState={baseLanes:n,cachePool:s},t!==null&&Jl(e,null),$u(),Xh(e),t!==null&&Mi(t,e,a,!0),null}function fs(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function To(t,e,n,a,s){return $n(e),n=eo(t,e,n,a,void 0,s),a=no(),t!==null&&!Zt?(ao(t,e,s),nn(t,e,s)):(yt&&a&&Lu(e),e.flags|=1,kt(t,e,n,s),e.child)}function id(t,e,n,a,s,r){return $n(e),e.updateQueue=null,n=ah(e,a,n,s),nh(t),a=no(),t!==null&&!Zt?(ao(t,e,r),nn(t,e,r)):(yt&&a&&Lu(e),e.flags|=1,kt(t,e,n,r),e.child)}function ld(t,e,n,a,s){if($n(e),e.stateNode===null){var r=Ra,f=n.contextType;typeof f=="object"&&f!==null&&(r=It(f)),r=new n(a,r),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=vo,e.stateNode=r,r._reactInternals=e,r=e.stateNode,r.props=a,r.state=e.memoizedState,r.refs={},Ju(e),f=n.contextType,r.context=typeof f=="object"&&f!==null?It(f):Ra,r.state=e.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(go(e,n,f,a),r.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(f=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),f!==r.state&&vo.enqueueReplaceState(r,r.state,null),_i(e,a,r,s),zi(),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){r=e.stateNode;var m=e.memoizedProps,S=ea(n,m);r.props=S;var D=r.context,C=n.contextType;f=Ra,typeof C=="object"&&C!==null&&(f=It(C));var _=n.getDerivedStateFromProps;C=typeof _=="function"||typeof r.getSnapshotBeforeUpdate=="function",m=e.pendingProps!==m,C||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(m||D!==f)&&Kh(e,r,a,f),gn=!1;var R=e.memoizedState;r.state=R,_i(e,a,r,s),zi(),D=e.memoizedState,m||R!==D||gn?(typeof _=="function"&&(go(e,n,_,a),D=e.memoizedState),(S=gn||Zh(e,n,S,a,R,D,f))?(C||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(e.flags|=4194308)):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=D),r.props=a,r.state=D,r.context=f,a=S):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{r=e.stateNode,Pu(t,e),f=e.memoizedProps,C=ea(n,f),r.props=C,_=e.pendingProps,R=r.context,D=n.contextType,S=Ra,typeof D=="object"&&D!==null&&(S=It(D)),m=n.getDerivedStateFromProps,(D=typeof m=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(f!==_||R!==S)&&Kh(e,r,a,S),gn=!1,R=e.memoizedState,r.state=R,_i(e,a,r,s),zi();var O=e.memoizedState;f!==_||R!==O||gn||t!==null&&t.dependencies!==null&&Ql(t.dependencies)?(typeof m=="function"&&(go(e,n,m,a),O=e.memoizedState),(C=gn||Zh(e,n,C,a,R,O,S)||t!==null&&t.dependencies!==null&&Ql(t.dependencies))?(D||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(a,O,S),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(a,O,S)),typeof r.componentDidUpdate=="function"&&(e.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof r.componentDidUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=O),r.props=a,r.state=O,r.context=S,a=C):(typeof r.componentDidUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),a=!1)}return r=a,fs(t,e),a=(e.flags&128)!==0,r||a?(r=e.stateNode,n=a&&typeof n.getDerivedStateFromError!="function"?null:r.render(),e.flags|=1,t!==null&&a?(e.child=wa(e,t.child,null,s),e.child=wa(e,null,n,s)):kt(t,e,n,s),e.memoizedState=r.state,t=e.child):t=nn(t,e,s),t}function sd(t,e,n,a){return xi(),e.flags|=256,kt(t,e,n,a),e.child}var bo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ao(t){return{baseLanes:t,cachePool:kf()}}function xo(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Re),t}function ud(t,e,n){var a=e.pendingProps,s=!1,r=(e.flags&128)!==0,f;if((f=r)||(f=t!==null&&t.memoizedState===null?!1:(Gt.current&2)!==0),f&&(s=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(yt){if(s?bn(e):An(),yt){var m=Ct,S;if(S=m){t:{for(S=m,m=Le;S.nodeType!==8;){if(!m){m=null;break t}if(S=Ne(S.nextSibling),S===null){m=null;break t}}m=S}m!==null?(e.memoizedState={dehydrated:m,treeContext:kn!==null?{id:Fe,overflow:We}:null,retryLane:536870912,hydrationErrors:null},S=me(18,null,null,0),S.stateNode=m,S.return=e,e.child=S,ne=e,Ct=null,S=!0):S=!1}S||Fn(e)}if(m=e.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return sr(m)?e.lanes=32:e.lanes=536870912,null;en(e)}return m=a.children,a=a.fallback,s?(An(),s=e.mode,m=hs({mode:"hidden",children:m},s),a=Qn(a,s,n,null),m.return=e,a.return=e,m.sibling=a,e.child=m,s=e.child,s.memoizedState=Ao(n),s.childLanes=xo(t,f,n),e.memoizedState=bo,a):(bn(e),Eo(e,m))}if(S=t.memoizedState,S!==null&&(m=S.dehydrated,m!==null)){if(r)e.flags&256?(bn(e),e.flags&=-257,e=Mo(t,e,n)):e.memoizedState!==null?(An(),e.child=t.child,e.flags|=128,e=null):(An(),s=a.fallback,m=e.mode,a=hs({mode:"visible",children:a.children},m),s=Qn(s,m,n,null),s.flags|=2,a.return=e,s.return=e,a.sibling=s,e.child=a,wa(e,t.child,null,n),a=e.child,a.memoizedState=Ao(n),a.childLanes=xo(t,f,n),e.memoizedState=bo,e=s);else if(bn(e),sr(m)){if(f=m.nextSibling&&m.nextSibling.dataset,f)var D=f.dgst;f=D,a=Error(o(419)),a.stack="",a.digest=f,Ei({value:a,source:null,stack:null}),e=Mo(t,e,n)}else if(Zt||Mi(t,e,n,!1),f=(n&t.childLanes)!==0,Zt||f){if(f=At,f!==null&&(a=n&-n,a=(a&42)!==0?1:su(a),a=(a&(f.suspendedLanes|n))!==0?0:a,a!==0&&a!==S.retryLane))throw S.retryLane=a,Da(t,a),Se(f,t,a),$h;m.data==="$?"||Xo(),e=Mo(t,e,n)}else m.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=S.treeContext,Ct=Ne(m.nextSibling),ne=e,yt=!0,Pn=null,Le=!1,t!==null&&(Ee[Me++]=Fe,Ee[Me++]=We,Ee[Me++]=kn,Fe=t.id,We=t.overflow,kn=e),e=Eo(e,a.children),e.flags|=4096);return e}return s?(An(),s=a.fallback,m=e.mode,S=t.child,D=S.sibling,a=Pe(S,{mode:"hidden",children:a.children}),a.subtreeFlags=S.subtreeFlags&65011712,D!==null?s=Pe(D,s):(s=Qn(s,m,n,null),s.flags|=2),s.return=e,a.return=e,a.sibling=s,e.child=a,a=s,s=e.child,m=t.child.memoizedState,m===null?m=Ao(n):(S=m.cachePool,S!==null?(D=Yt._currentValue,S=S.parent!==D?{parent:D,pool:D}:S):S=kf(),m={baseLanes:m.baseLanes|n,cachePool:S}),s.memoizedState=m,s.childLanes=xo(t,f,n),e.memoizedState=bo,a):(bn(e),n=t.child,t=n.sibling,n=Pe(n,{mode:"visible",children:a.children}),n.return=e,n.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=n,e.memoizedState=null,n)}function Eo(t,e){return e=hs({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function hs(t,e){return t=me(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Mo(t,e,n){return wa(e,t.child,null,n),t=Eo(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function od(t,e,n){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Gu(t.return,e,n)}function Do(t,e,n,a,s){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:s}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=a,r.tail=n,r.tailMode=s)}function rd(t,e,n){var a=e.pendingProps,s=a.revealOrder,r=a.tail;if(kt(t,e,a.children,n),a=Gt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&od(t,n,e);else if(t.tag===19)od(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(G(Gt,a),s){case"forwards":for(n=e.child,s=null;n!==null;)t=n.alternate,t!==null&&os(t)===null&&(s=n),n=n.sibling;n=s,n===null?(s=e.child,e.child=null):(s=n.sibling,n.sibling=null),Do(e,!1,s,n,r);break;case"backwards":for(n=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&os(t)===null){e.child=s;break}t=s.sibling,s.sibling=n,n=s,s=t}Do(e,!0,n,null,r);break;case"together":Do(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function nn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Rn|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Mi(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,n=Pe(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Pe(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Ro(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Ql(t)))}function ev(t,e,n){switch(e.tag){case 3:Et(e,e.stateNode.containerInfo),pn(e,Yt,t.memoizedState.cache),xi();break;case 27:case 5:eu(e);break;case 4:Et(e,e.stateNode.containerInfo);break;case 10:pn(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(bn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?ud(t,e,n):(bn(e),t=nn(t,e,n),t!==null?t.sibling:null);bn(e);break;case 19:var s=(t.flags&128)!==0;if(a=(n&e.childLanes)!==0,a||(Mi(t,e,n,!1),a=(n&e.childLanes)!==0),s){if(a)return rd(t,e,n);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),G(Gt,Gt.current),a)break;return null;case 22:case 23:return e.lanes=0,nd(t,e,n);case 24:pn(e,Yt,t.memoizedState.cache)}return nn(t,e,n)}function cd(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Zt=!0;else{if(!Ro(t,n)&&(e.flags&128)===0)return Zt=!1,ev(t,e,n);Zt=(t.flags&131072)!==0}else Zt=!1,yt&&(e.flags&1048576)!==0&&qf(e,Kl,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,s=a._init;if(a=s(a._payload),e.type=a,typeof a=="function")Bu(a)?(t=ea(a,t),e.tag=1,e=ld(null,e,a,t,n)):(e.tag=0,e=To(null,e,a,t,n));else{if(a!=null){if(s=a.$$typeof,s===Q){e.tag=11,e=Ih(null,e,a,t,n);break t}else if(s===it){e.tag=14,e=td(null,e,a,t,n);break t}}throw e=je(a)||a,Error(o(306,e,""))}}return e;case 0:return To(t,e,e.type,e.pendingProps,n);case 1:return a=e.type,s=ea(a,e.pendingProps),ld(t,e,a,s,n);case 3:t:{if(Et(e,e.stateNode.containerInfo),t===null)throw Error(o(387));a=e.pendingProps;var r=e.memoizedState;s=r.element,Pu(t,e),_i(e,a,null,n);var f=e.memoizedState;if(a=f.cache,pn(e,Yt,a),a!==r.cache&&Xu(e,[Yt],n,!0),zi(),a=f.element,r.isDehydrated)if(r={element:a,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=sd(t,e,a,n);break t}else if(a!==s){s=Ae(Error(o(424)),e),Ei(s),e=sd(t,e,a,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ct=Ne(t.firstChild),ne=e,yt=!0,Pn=null,Le=!0,n=Gh(e,null,a,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(xi(),a===s){e=nn(t,e,n);break t}kt(t,e,a,n)}e=e.child}return e;case 26:return fs(t,e),t===null?(n=mm(e.type,null,e.pendingProps,null))?e.memoizedState=n:yt||(n=e.type,t=e.pendingProps,a=Ds(lt.current).createElement(n),a[$t]=e,a[ie]=t,Pt(a,n,t),Xt(a),e.stateNode=a):e.memoizedState=mm(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return eu(e),t===null&&yt&&(a=e.stateNode=fm(e.type,e.pendingProps,lt.current),ne=e,Le=!0,s=Ct,zn(e.type)?(ur=s,Ct=Ne(a.firstChild)):Ct=s),kt(t,e,e.pendingProps.children,n),fs(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&yt&&((s=a=Ct)&&(a=Cv(a,e.type,e.pendingProps,Le),a!==null?(e.stateNode=a,ne=e,Ct=Ne(a.firstChild),Le=!1,s=!0):s=!1),s||Fn(e)),eu(e),s=e.type,r=e.pendingProps,f=t!==null?t.memoizedProps:null,a=r.children,ar(s,r)?a=null:f!==null&&ar(s,f)&&(e.flags|=32),e.memoizedState!==null&&(s=eo(t,e,k0,null,null,n),tl._currentValue=s),fs(t,e),kt(t,e,a,n),e.child;case 6:return t===null&&yt&&((t=n=Ct)&&(n=Vv(n,e.pendingProps,Le),n!==null?(e.stateNode=n,ne=e,Ct=null,t=!0):t=!1),t||Fn(e)),null;case 13:return ud(t,e,n);case 4:return Et(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=wa(e,null,a,n):kt(t,e,a,n),e.child;case 11:return Ih(t,e,e.type,e.pendingProps,n);case 7:return kt(t,e,e.pendingProps,n),e.child;case 8:return kt(t,e,e.pendingProps.children,n),e.child;case 12:return kt(t,e,e.pendingProps.children,n),e.child;case 10:return a=e.pendingProps,pn(e,e.type,a.value),kt(t,e,a.children,n),e.child;case 9:return s=e.type._context,a=e.pendingProps.children,$n(e),s=It(s),a=a(s),e.flags|=1,kt(t,e,a,n),e.child;case 14:return td(t,e,e.type,e.pendingProps,n);case 15:return ed(t,e,e.type,e.pendingProps,n);case 19:return rd(t,e,n);case 31:return a=e.pendingProps,n=e.mode,a={mode:a.mode,children:a.children},t===null?(n=hs(a,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=Pe(t.child,a),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return nd(t,e,n);case 24:return $n(e),a=It(Yt),t===null?(s=Qu(),s===null&&(s=At,r=Zu(),s.pooledCache=r,r.refCount++,r!==null&&(s.pooledCacheLanes|=n),s=r),e.memoizedState={parent:a,cache:s},Ju(e),pn(e,Yt,s)):((t.lanes&n)!==0&&(Pu(t,e),_i(e,null,null,n),zi()),s=t.memoizedState,r=e.memoizedState,s.parent!==a?(s={parent:a,cache:a},e.memoizedState=s,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=s),pn(e,Yt,a)):(a=r.cache,pn(e,Yt,a),a!==s.cache&&Xu(e,[Yt],n,!0))),kt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function an(t){t.flags|=4}function fd(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Sm(e)){if(e=De.current,e!==null&&((ht&4194048)===ht?He!==null:(ht&62914560)!==ht&&(ht&536870912)===0||e!==He))throw Ci=ku,Jf;t.flags|=8192}}function ds(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Gc():536870912,t.lanes|=e,Ya|=e)}function Hi(t,e){if(!yt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Rt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,a=0;if(e)for(var s=t.child;s!==null;)n|=s.lanes|s.childLanes,a|=s.subtreeFlags&65011712,a|=s.flags&65011712,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)n|=s.lanes|s.childLanes,a|=s.subtreeFlags,a|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=a,t.childLanes=n,e}function nv(t,e,n){var a=e.pendingProps;switch(Hu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Rt(e),null;case 1:return Rt(e),null;case 3:return n=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Ie(Yt),hn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Ai(e)?an(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Xf())),Rt(e),null;case 26:return n=e.memoizedState,t===null?(an(e),n!==null?(Rt(e),fd(e,n)):(Rt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(an(e),Rt(e),fd(e,n)):(Rt(e),e.flags&=-16777217):(t.memoizedProps!==a&&an(e),Rt(e),e.flags&=-16777217),null;case 27:El(e),n=lt.current;var s=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&an(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Rt(e),null}t=$.current,Ai(e)?Yf(e):(t=fm(s,a,n),e.stateNode=t,an(e))}return Rt(e),null;case 5:if(El(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&an(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Rt(e),null}if(t=$.current,Ai(e))Yf(e);else{switch(s=Ds(lt.current),t){case 1:t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=s.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?s.createElement("select",{is:a.is}):s.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?s.createElement(n,{is:a.is}):s.createElement(n)}}t[$t]=e,t[ie]=a;t:for(s=e.child;s!==null;){if(s.tag===5||s.tag===6)t.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===e)break t;for(;s.sibling===null;){if(s.return===null||s.return===e)break t;s=s.return}s.sibling.return=s.return,s=s.sibling}e.stateNode=t;t:switch(Pt(t,n,a),n){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&an(e)}}return Rt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&an(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(o(166));if(t=lt.current,Ai(e)){if(t=e.stateNode,n=e.memoizedProps,a=null,s=ne,s!==null)switch(s.tag){case 27:case 5:a=s.memoizedProps}t[$t]=e,t=!!(t.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||im(t.nodeValue,n)),t||Fn(e)}else t=Ds(t).createTextNode(a),t[$t]=e,e.stateNode=t}return Rt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(s=Ai(e),a!==null&&a.dehydrated!==null){if(t===null){if(!s)throw Error(o(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(o(317));s[$t]=e}else xi(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Rt(e),s=!1}else s=Xf(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=s),s=!0;if(!s)return e.flags&256?(en(e),e):(en(e),null)}if(en(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=a!==null,t=t!==null&&t.memoizedState!==null,n){a=e.child,s=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(s=a.alternate.memoizedState.cachePool.pool);var r=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(r=a.memoizedState.cachePool.pool),r!==s&&(a.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),ds(e,e.updateQueue),Rt(e),null;case 4:return hn(),t===null&&$o(e.stateNode.containerInfo),Rt(e),null;case 10:return Ie(e.type),Rt(e),null;case 19:if(Z(Gt),s=e.memoizedState,s===null)return Rt(e),null;if(a=(e.flags&128)!==0,r=s.rendering,r===null)if(a)Hi(s,!1);else{if(Vt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(r=os(t),r!==null){for(e.flags|=128,Hi(s,!1),t=r.updateQueue,e.updateQueue=t,ds(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)Hf(n,t),n=n.sibling;return G(Gt,Gt.current&1|2),e.child}t=t.sibling}s.tail!==null&&we()>ps&&(e.flags|=128,a=!0,Hi(s,!1),e.lanes=4194304)}else{if(!a)if(t=os(r),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,ds(e,t),Hi(s,!0),s.tail===null&&s.tailMode==="hidden"&&!r.alternate&&!yt)return Rt(e),null}else 2*we()-s.renderingStartTime>ps&&n!==536870912&&(e.flags|=128,a=!0,Hi(s,!1),e.lanes=4194304);s.isBackwards?(r.sibling=e.child,e.child=r):(t=s.last,t!==null?t.sibling=r:e.child=r,s.last=r)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=we(),e.sibling=null,t=Gt.current,G(Gt,a?t&1|2:t&1),e):(Rt(e),null);case 22:case 23:return en(e),Iu(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(n&536870912)!==0&&(e.flags&128)===0&&(Rt(e),e.subtreeFlags&6&&(e.flags|=8192)):Rt(e),n=e.updateQueue,n!==null&&ds(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==n&&(e.flags|=2048),t!==null&&Z(In),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Ie(Yt),Rt(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function av(t,e){switch(Hu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Ie(Yt),hn(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return El(e),null;case 13:if(en(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));xi()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Z(Gt),null;case 4:return hn(),null;case 10:return Ie(e.type),null;case 22:case 23:return en(e),Iu(),t!==null&&Z(In),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Ie(Yt),null;case 25:return null;default:return null}}function hd(t,e){switch(Hu(e),e.tag){case 3:Ie(Yt),hn();break;case 26:case 27:case 5:El(e);break;case 4:hn();break;case 13:en(e);break;case 19:Z(Gt);break;case 10:Ie(e.type);break;case 22:case 23:en(e),Iu(),t!==null&&Z(In);break;case 24:Ie(Yt)}}function qi(t,e){try{var n=e.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var s=a.next;n=s;do{if((n.tag&t)===t){a=void 0;var r=n.create,f=n.inst;a=r(),f.destroy=a}n=n.next}while(n!==s)}}catch(m){bt(e,e.return,m)}}function xn(t,e,n){try{var a=e.updateQueue,s=a!==null?a.lastEffect:null;if(s!==null){var r=s.next;a=r;do{if((a.tag&t)===t){var f=a.inst,m=f.destroy;if(m!==void 0){f.destroy=void 0,s=e;var S=n,D=m;try{D()}catch(C){bt(s,S,C)}}}a=a.next}while(a!==r)}}catch(C){bt(e,e.return,C)}}function dd(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{th(e,n)}catch(a){bt(t,t.return,a)}}}function md(t,e,n){n.props=ea(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(a){bt(t,e,a)}}function Yi(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof n=="function"?t.refCleanup=n(a):n.current=a}}catch(s){bt(t,e,s)}}function qe(t,e){var n=t.ref,a=t.refCleanup;if(n!==null)if(typeof a=="function")try{a()}catch(s){bt(t,e,s)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(s){bt(t,e,s)}else n.current=null}function yd(t){var e=t.type,n=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break t;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(s){bt(t,t.return,s)}}function Oo(t,e,n){try{var a=t.stateNode;Ev(a,t.type,n,e),a[ie]=e}catch(s){bt(t,t.return,s)}}function pd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&zn(t.type)||t.tag===4}function Co(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||pd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&zn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Vo(t,e,n){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Ms));else if(a!==4&&(a===27&&zn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(Vo(t,e,n),t=t.sibling;t!==null;)Vo(t,e,n),t=t.sibling}function ms(t,e,n){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(a!==4&&(a===27&&zn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(ms(t,e,n),t=t.sibling;t!==null;)ms(t,e,n),t=t.sibling}function gd(t){var e=t.stateNode,n=t.memoizedProps;try{for(var a=t.type,s=e.attributes;s.length;)e.removeAttributeNode(s[0]);Pt(e,a,n),e[$t]=t,e[ie]=n}catch(r){bt(t,t.return,r)}}var ln=!1,Ut=!1,zo=!1,vd=typeof WeakSet=="function"?WeakSet:Set,Kt=null;function iv(t,e){if(t=t.containerInfo,er=_s,t=Cf(t),Ou(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var s=a.anchorOffset,r=a.focusNode;a=a.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break t}var f=0,m=-1,S=-1,D=0,C=0,_=t,R=null;e:for(;;){for(var O;_!==n||s!==0&&_.nodeType!==3||(m=f+s),_!==r||a!==0&&_.nodeType!==3||(S=f+a),_.nodeType===3&&(f+=_.nodeValue.length),(O=_.firstChild)!==null;)R=_,_=O;for(;;){if(_===t)break e;if(R===n&&++D===s&&(m=f),R===r&&++C===a&&(S=f),(O=_.nextSibling)!==null)break;_=R,R=_.parentNode}_=O}n=m===-1||S===-1?null:{start:m,end:S}}else n=null}n=n||{start:0,end:0}}else n=null;for(nr={focusedElem:t,selectionRange:n},_s=!1,Kt=e;Kt!==null;)if(e=Kt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Kt=t;else for(;Kt!==null;){switch(e=Kt,r=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&r!==null){t=void 0,n=e,s=r.memoizedProps,r=r.memoizedState,a=n.stateNode;try{var nt=ea(n.type,s,n.elementType===n.type);t=a.getSnapshotBeforeUpdate(nt,r),a.__reactInternalSnapshotBeforeUpdate=t}catch(I){bt(n,n.return,I)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)lr(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":lr(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,Kt=t;break}Kt=e.return}}function Sd(t,e,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:En(t,n),a&4&&qi(5,n);break;case 1:if(En(t,n),a&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(f){bt(n,n.return,f)}else{var s=ea(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(s,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){bt(n,n.return,f)}}a&64&&dd(n),a&512&&Yi(n,n.return);break;case 3:if(En(t,n),a&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{th(t,e)}catch(f){bt(n,n.return,f)}}break;case 27:e===null&&a&4&&gd(n);case 26:case 5:En(t,n),e===null&&a&4&&yd(n),a&512&&Yi(n,n.return);break;case 12:En(t,n);break;case 13:En(t,n),a&4&&Ad(t,n),a&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=dv.bind(null,n),zv(t,n))));break;case 22:if(a=n.memoizedState!==null||ln,!a){e=e!==null&&e.memoizedState!==null||Ut,s=ln;var r=Ut;ln=a,(Ut=e)&&!r?Mn(t,n,(n.subtreeFlags&8772)!==0):En(t,n),ln=s,Ut=r}break;case 30:break;default:En(t,n)}}function Td(t){var e=t.alternate;e!==null&&(t.alternate=null,Td(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&ru(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Mt=null,ue=!1;function sn(t,e,n){for(n=n.child;n!==null;)bd(t,e,n),n=n.sibling}function bd(t,e,n){if(fe&&typeof fe.onCommitFiberUnmount=="function")try{fe.onCommitFiberUnmount(oi,n)}catch{}switch(n.tag){case 26:Ut||qe(n,e),sn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Ut||qe(n,e);var a=Mt,s=ue;zn(n.type)&&(Mt=n.stateNode,ue=!1),sn(t,e,n),Fi(n.stateNode),Mt=a,ue=s;break;case 5:Ut||qe(n,e);case 6:if(a=Mt,s=ue,Mt=null,sn(t,e,n),Mt=a,ue=s,Mt!==null)if(ue)try{(Mt.nodeType===9?Mt.body:Mt.nodeName==="HTML"?Mt.ownerDocument.body:Mt).removeChild(n.stateNode)}catch(r){bt(n,e,r)}else try{Mt.removeChild(n.stateNode)}catch(r){bt(n,e,r)}break;case 18:Mt!==null&&(ue?(t=Mt,rm(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),il(t)):rm(Mt,n.stateNode));break;case 4:a=Mt,s=ue,Mt=n.stateNode.containerInfo,ue=!0,sn(t,e,n),Mt=a,ue=s;break;case 0:case 11:case 14:case 15:Ut||xn(2,n,e),Ut||xn(4,n,e),sn(t,e,n);break;case 1:Ut||(qe(n,e),a=n.stateNode,typeof a.componentWillUnmount=="function"&&md(n,e,a)),sn(t,e,n);break;case 21:sn(t,e,n);break;case 22:Ut=(a=Ut)||n.memoizedState!==null,sn(t,e,n),Ut=a;break;default:sn(t,e,n)}}function Ad(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{il(t)}catch(n){bt(e,e.return,n)}}function lv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new vd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new vd),e;default:throw Error(o(435,t.tag))}}function _o(t,e){var n=lv(t);e.forEach(function(a){var s=mv.bind(null,t,a);n.has(a)||(n.add(a),a.then(s,s))})}function ye(t,e){var n=e.deletions;if(n!==null)for(var a=0;a<n.length;a++){var s=n[a],r=t,f=e,m=f;t:for(;m!==null;){switch(m.tag){case 27:if(zn(m.type)){Mt=m.stateNode,ue=!1;break t}break;case 5:Mt=m.stateNode,ue=!1;break t;case 3:case 4:Mt=m.stateNode.containerInfo,ue=!0;break t}m=m.return}if(Mt===null)throw Error(o(160));bd(r,f,s),Mt=null,ue=!1,r=s.alternate,r!==null&&(r.return=null),s.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)xd(e,t),e=e.sibling}var Ue=null;function xd(t,e){var n=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ye(e,t),pe(t),a&4&&(xn(3,t,t.return),qi(3,t),xn(5,t,t.return));break;case 1:ye(e,t),pe(t),a&512&&(Ut||n===null||qe(n,n.return)),a&64&&ln&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var s=Ue;if(ye(e,t),pe(t),a&512&&(Ut||n===null||qe(n,n.return)),a&4){var r=n!==null?n.memoizedState:null;if(a=t.memoizedState,n===null)if(a===null)if(t.stateNode===null){t:{a=t.type,n=t.memoizedProps,s=s.ownerDocument||s;e:switch(a){case"title":r=s.getElementsByTagName("title")[0],(!r||r[fi]||r[$t]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=s.createElement(a),s.head.insertBefore(r,s.querySelector("head > title"))),Pt(r,a,n),r[$t]=t,Xt(r),a=r;break t;case"link":var f=gm("link","href",s).get(a+(n.href||""));if(f){for(var m=0;m<f.length;m++)if(r=f[m],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(m,1);break e}}r=s.createElement(a),Pt(r,a,n),s.head.appendChild(r);break;case"meta":if(f=gm("meta","content",s).get(a+(n.content||""))){for(m=0;m<f.length;m++)if(r=f[m],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(m,1);break e}}r=s.createElement(a),Pt(r,a,n),s.head.appendChild(r);break;default:throw Error(o(468,a))}r[$t]=t,Xt(r),a=r}t.stateNode=a}else vm(s,t.type,t.stateNode);else t.stateNode=pm(s,a,t.memoizedProps);else r!==a?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,a===null?vm(s,t.type,t.stateNode):pm(s,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Oo(t,t.memoizedProps,n.memoizedProps)}break;case 27:ye(e,t),pe(t),a&512&&(Ut||n===null||qe(n,n.return)),n!==null&&a&4&&Oo(t,t.memoizedProps,n.memoizedProps);break;case 5:if(ye(e,t),pe(t),a&512&&(Ut||n===null||qe(n,n.return)),t.flags&32){s=t.stateNode;try{Sa(s,"")}catch(O){bt(t,t.return,O)}}a&4&&t.stateNode!=null&&(s=t.memoizedProps,Oo(t,s,n!==null?n.memoizedProps:s)),a&1024&&(zo=!0);break;case 6:if(ye(e,t),pe(t),a&4){if(t.stateNode===null)throw Error(o(162));a=t.memoizedProps,n=t.stateNode;try{n.nodeValue=a}catch(O){bt(t,t.return,O)}}break;case 3:if(Cs=null,s=Ue,Ue=Rs(e.containerInfo),ye(e,t),Ue=s,pe(t),a&4&&n!==null&&n.memoizedState.isDehydrated)try{il(e.containerInfo)}catch(O){bt(t,t.return,O)}zo&&(zo=!1,Ed(t));break;case 4:a=Ue,Ue=Rs(t.stateNode.containerInfo),ye(e,t),pe(t),Ue=a;break;case 12:ye(e,t),pe(t);break;case 13:ye(e,t),pe(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Lo=we()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,_o(t,a)));break;case 22:s=t.memoizedState!==null;var S=n!==null&&n.memoizedState!==null,D=ln,C=Ut;if(ln=D||s,Ut=C||S,ye(e,t),Ut=C,ln=D,pe(t),a&8192)t:for(e=t.stateNode,e._visibility=s?e._visibility&-2:e._visibility|1,s&&(n===null||S||ln||Ut||na(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){S=n=e;try{if(r=S.stateNode,s)f=r.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{m=S.stateNode;var _=S.memoizedProps.style,R=_!=null&&_.hasOwnProperty("display")?_.display:null;m.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(O){bt(S,S.return,O)}}}else if(e.tag===6){if(n===null){S=e;try{S.stateNode.nodeValue=s?"":S.memoizedProps}catch(O){bt(S,S.return,O)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,_o(t,n))));break;case 19:ye(e,t),pe(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,_o(t,a)));break;case 30:break;case 21:break;default:ye(e,t),pe(t)}}function pe(t){var e=t.flags;if(e&2){try{for(var n,a=t.return;a!==null;){if(pd(a)){n=a;break}a=a.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var s=n.stateNode,r=Co(t);ms(t,r,s);break;case 5:var f=n.stateNode;n.flags&32&&(Sa(f,""),n.flags&=-33);var m=Co(t);ms(t,m,f);break;case 3:case 4:var S=n.stateNode.containerInfo,D=Co(t);Vo(t,D,S);break;default:throw Error(o(161))}}catch(C){bt(t,t.return,C)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Ed(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Ed(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function En(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Sd(t,e.alternate,e),e=e.sibling}function na(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:xn(4,e,e.return),na(e);break;case 1:qe(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&md(e,e.return,n),na(e);break;case 27:Fi(e.stateNode);case 26:case 5:qe(e,e.return),na(e);break;case 22:e.memoizedState===null&&na(e);break;case 30:na(e);break;default:na(e)}t=t.sibling}}function Mn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,s=t,r=e,f=r.flags;switch(r.tag){case 0:case 11:case 15:Mn(s,r,n),qi(4,r);break;case 1:if(Mn(s,r,n),a=r,s=a.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(D){bt(a,a.return,D)}if(a=r,s=a.updateQueue,s!==null){var m=a.stateNode;try{var S=s.shared.hiddenCallbacks;if(S!==null)for(s.shared.hiddenCallbacks=null,s=0;s<S.length;s++)If(S[s],m)}catch(D){bt(a,a.return,D)}}n&&f&64&&dd(r),Yi(r,r.return);break;case 27:gd(r);case 26:case 5:Mn(s,r,n),n&&a===null&&f&4&&yd(r),Yi(r,r.return);break;case 12:Mn(s,r,n);break;case 13:Mn(s,r,n),n&&f&4&&Ad(s,r);break;case 22:r.memoizedState===null&&Mn(s,r,n),Yi(r,r.return);break;case 30:break;default:Mn(s,r,n)}e=e.sibling}}function Uo(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Di(n))}function No(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Di(t))}function Ye(t,e,n,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Md(t,e,n,a),e=e.sibling}function Md(t,e,n,a){var s=e.flags;switch(e.tag){case 0:case 11:case 15:Ye(t,e,n,a),s&2048&&qi(9,e);break;case 1:Ye(t,e,n,a);break;case 3:Ye(t,e,n,a),s&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Di(t)));break;case 12:if(s&2048){Ye(t,e,n,a),t=e.stateNode;try{var r=e.memoizedProps,f=r.id,m=r.onPostCommit;typeof m=="function"&&m(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(S){bt(e,e.return,S)}}else Ye(t,e,n,a);break;case 13:Ye(t,e,n,a);break;case 23:break;case 22:r=e.stateNode,f=e.alternate,e.memoizedState!==null?r._visibility&2?Ye(t,e,n,a):Gi(t,e):r._visibility&2?Ye(t,e,n,a):(r._visibility|=2,La(t,e,n,a,(e.subtreeFlags&10256)!==0)),s&2048&&Uo(f,e);break;case 24:Ye(t,e,n,a),s&2048&&No(e.alternate,e);break;default:Ye(t,e,n,a)}}function La(t,e,n,a,s){for(s=s&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var r=t,f=e,m=n,S=a,D=f.flags;switch(f.tag){case 0:case 11:case 15:La(r,f,m,S,s),qi(8,f);break;case 23:break;case 22:var C=f.stateNode;f.memoizedState!==null?C._visibility&2?La(r,f,m,S,s):Gi(r,f):(C._visibility|=2,La(r,f,m,S,s)),s&&D&2048&&Uo(f.alternate,f);break;case 24:La(r,f,m,S,s),s&&D&2048&&No(f.alternate,f);break;default:La(r,f,m,S,s)}e=e.sibling}}function Gi(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,a=e,s=a.flags;switch(a.tag){case 22:Gi(n,a),s&2048&&Uo(a.alternate,a);break;case 24:Gi(n,a),s&2048&&No(a.alternate,a);break;default:Gi(n,a)}e=e.sibling}}var Xi=8192;function Ha(t){if(t.subtreeFlags&Xi)for(t=t.child;t!==null;)Dd(t),t=t.sibling}function Dd(t){switch(t.tag){case 26:Ha(t),t.flags&Xi&&t.memoizedState!==null&&Zv(Ue,t.memoizedState,t.memoizedProps);break;case 5:Ha(t);break;case 3:case 4:var e=Ue;Ue=Rs(t.stateNode.containerInfo),Ha(t),Ue=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Xi,Xi=16777216,Ha(t),Xi=e):Ha(t));break;default:Ha(t)}}function Rd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Zi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var a=e[n];Kt=a,Cd(a,t)}Rd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Od(t),t=t.sibling}function Od(t){switch(t.tag){case 0:case 11:case 15:Zi(t),t.flags&2048&&xn(9,t,t.return);break;case 3:Zi(t);break;case 12:Zi(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ys(t)):Zi(t);break;default:Zi(t)}}function ys(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var a=e[n];Kt=a,Cd(a,t)}Rd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:xn(8,e,e.return),ys(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,ys(e));break;default:ys(e)}t=t.sibling}}function Cd(t,e){for(;Kt!==null;){var n=Kt;switch(n.tag){case 0:case 11:case 15:xn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var a=n.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Di(n.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,Kt=a;else t:for(n=t;Kt!==null;){a=Kt;var s=a.sibling,r=a.return;if(Td(a),a===n){Kt=null;break t}if(s!==null){s.return=r,Kt=s;break t}Kt=r}}}var sv={getCacheForType:function(t){var e=It(Yt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},uv=typeof WeakMap=="function"?WeakMap:Map,pt=0,At=null,rt=null,ht=0,gt=0,ge=null,Dn=!1,qa=!1,Bo=!1,un=0,Vt=0,Rn=0,aa=0,jo=0,Re=0,Ya=0,Ki=null,oe=null,wo=!1,Lo=0,ps=1/0,gs=null,On=null,Jt=0,Cn=null,Ga=null,Xa=0,Ho=0,qo=null,Vd=null,Qi=0,Yo=null;function ve(){if((pt&2)!==0&&ht!==0)return ht&-ht;if(V.T!==null){var t=Va;return t!==0?t:Jo()}return Kc()}function zd(){Re===0&&(Re=(ht&536870912)===0||yt?Yc():536870912);var t=De.current;return t!==null&&(t.flags|=32),Re}function Se(t,e,n){(t===At&&(gt===2||gt===9)||t.cancelPendingCommit!==null)&&(Za(t,0),Vn(t,ht,Re,!1)),ci(t,n),((pt&2)===0||t!==At)&&(t===At&&((pt&2)===0&&(aa|=n),Vt===4&&Vn(t,ht,Re,!1)),Ge(t))}function _d(t,e,n){if((pt&6)!==0)throw Error(o(327));var a=!n&&(e&124)===0&&(e&t.expiredLanes)===0||ri(t,e),s=a?cv(t,e):Zo(t,e,!0),r=a;do{if(s===0){qa&&!a&&Vn(t,e,0,!1);break}else{if(n=t.current.alternate,r&&!ov(n)){s=Zo(t,e,!1),r=!1;continue}if(s===2){if(r=e,t.errorRecoveryDisabledLanes&r)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var m=t;s=Ki;var S=m.current.memoizedState.isDehydrated;if(S&&(Za(m,f).flags|=256),f=Zo(m,f,!1),f!==2){if(Bo&&!S){m.errorRecoveryDisabledLanes|=r,aa|=r,s=4;break t}r=oe,oe=s,r!==null&&(oe===null?oe=r:oe.push.apply(oe,r))}s=f}if(r=!1,s!==2)continue}}if(s===1){Za(t,0),Vn(t,e,0,!0);break}t:{switch(a=t,r=s,r){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:Vn(a,e,Re,!Dn);break t;case 2:oe=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(s=Lo+300-we(),10<s)){if(Vn(a,e,Re,!Dn),Ol(a,0,!0)!==0)break t;a.timeoutHandle=um(Ud.bind(null,a,n,oe,gs,wo,e,Re,aa,Ya,Dn,r,2,-0,0),s);break t}Ud(a,n,oe,gs,wo,e,Re,aa,Ya,Dn,r,0,-0,0)}}break}while(!0);Ge(t)}function Ud(t,e,n,a,s,r,f,m,S,D,C,_,R,O){if(t.timeoutHandle=-1,_=e.subtreeFlags,(_&8192||(_&16785408)===16785408)&&(Ii={stylesheets:null,count:0,unsuspend:Xv},Dd(e),_=Kv(),_!==null)){t.cancelPendingCommit=_(qd.bind(null,t,e,r,n,a,s,f,m,S,C,1,R,O)),Vn(t,r,f,!D);return}qd(t,e,r,n,a,s,f,m,S)}function ov(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var s=n[a],r=s.getSnapshot;s=s.value;try{if(!de(r(),s))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Vn(t,e,n,a){e&=~jo,e&=~aa,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var s=e;0<s;){var r=31-he(s),f=1<<r;a[r]=-1,s&=~f}n!==0&&Xc(t,n,e)}function vs(){return(pt&6)===0?(ki(0),!1):!0}function Go(){if(rt!==null){if(gt===0)var t=rt.return;else t=rt,$e=Wn=null,io(t),ja=null,wi=0,t=rt;for(;t!==null;)hd(t.alternate,t),t=t.return;rt=null}}function Za(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Dv(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Go(),At=t,rt=n=Pe(t.current,null),ht=e,gt=0,ge=null,Dn=!1,qa=ri(t,e),Bo=!1,Ya=Re=jo=aa=Rn=Vt=0,oe=Ki=null,wo=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var s=31-he(a),r=1<<s;e|=t[s],a&=~r}return un=e,ql(),n}function Nd(t,e){ut=null,V.H=ls,e===Oi||e===Pl?(e=Wf(),gt=3):e===Jf?(e=Wf(),gt=4):gt=e===$h?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ge=e,rt===null&&(Vt=1,cs(t,Ae(e,t.current)))}function Bd(){var t=V.H;return V.H=ls,t===null?ls:t}function jd(){var t=V.A;return V.A=sv,t}function Xo(){Vt=4,Dn||(ht&4194048)!==ht&&De.current!==null||(qa=!0),(Rn&134217727)===0&&(aa&134217727)===0||At===null||Vn(At,ht,Re,!1)}function Zo(t,e,n){var a=pt;pt|=2;var s=Bd(),r=jd();(At!==t||ht!==e)&&(gs=null,Za(t,e)),e=!1;var f=Vt;t:do try{if(gt!==0&&rt!==null){var m=rt,S=ge;switch(gt){case 8:Go(),f=6;break t;case 3:case 2:case 9:case 6:De.current===null&&(e=!0);var D=gt;if(gt=0,ge=null,Ka(t,m,S,D),n&&qa){f=0;break t}break;default:D=gt,gt=0,ge=null,Ka(t,m,S,D)}}rv(),f=Vt;break}catch(C){Nd(t,C)}while(!0);return e&&t.shellSuspendCounter++,$e=Wn=null,pt=a,V.H=s,V.A=r,rt===null&&(At=null,ht=0,ql()),f}function rv(){for(;rt!==null;)wd(rt)}function cv(t,e){var n=pt;pt|=2;var a=Bd(),s=jd();At!==t||ht!==e?(gs=null,ps=we()+500,Za(t,e)):qa=ri(t,e);t:do try{if(gt!==0&&rt!==null){e=rt;var r=ge;e:switch(gt){case 1:gt=0,ge=null,Ka(t,e,r,1);break;case 2:case 9:if(Pf(r)){gt=0,ge=null,Ld(e);break}e=function(){gt!==2&&gt!==9||At!==t||(gt=7),Ge(t)},r.then(e,e);break t;case 3:gt=7;break t;case 4:gt=5;break t;case 7:Pf(r)?(gt=0,ge=null,Ld(e)):(gt=0,ge=null,Ka(t,e,r,7));break;case 5:var f=null;switch(rt.tag){case 26:f=rt.memoizedState;case 5:case 27:var m=rt;if(!f||Sm(f)){gt=0,ge=null;var S=m.sibling;if(S!==null)rt=S;else{var D=m.return;D!==null?(rt=D,Ss(D)):rt=null}break e}}gt=0,ge=null,Ka(t,e,r,5);break;case 6:gt=0,ge=null,Ka(t,e,r,6);break;case 8:Go(),Vt=6;break t;default:throw Error(o(462))}}fv();break}catch(C){Nd(t,C)}while(!0);return $e=Wn=null,V.H=a,V.A=s,pt=n,rt!==null?0:(At=null,ht=0,ql(),Vt)}function fv(){for(;rt!==null&&!Ug();)wd(rt)}function wd(t){var e=cd(t.alternate,t,un);t.memoizedProps=t.pendingProps,e===null?Ss(t):rt=e}function Ld(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=id(n,e,e.pendingProps,e.type,void 0,ht);break;case 11:e=id(n,e,e.pendingProps,e.type.render,e.ref,ht);break;case 5:io(e);default:hd(n,e),e=rt=Hf(e,un),e=cd(n,e,un)}t.memoizedProps=t.pendingProps,e===null?Ss(t):rt=e}function Ka(t,e,n,a){$e=Wn=null,io(e),ja=null,wi=0;var s=e.return;try{if(tv(t,s,e,n,ht)){Vt=1,cs(t,Ae(n,t.current)),rt=null;return}}catch(r){if(s!==null)throw rt=s,r;Vt=1,cs(t,Ae(n,t.current)),rt=null;return}e.flags&32768?(yt||a===1?t=!0:qa||(ht&536870912)!==0?t=!1:(Dn=t=!0,(a===2||a===9||a===3||a===6)&&(a=De.current,a!==null&&a.tag===13&&(a.flags|=16384))),Hd(e,t)):Ss(e)}function Ss(t){var e=t;do{if((e.flags&32768)!==0){Hd(e,Dn);return}t=e.return;var n=nv(e.alternate,e,un);if(n!==null){rt=n;return}if(e=e.sibling,e!==null){rt=e;return}rt=e=t}while(e!==null);Vt===0&&(Vt=5)}function Hd(t,e){do{var n=av(t.alternate,t);if(n!==null){n.flags&=32767,rt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){rt=t;return}rt=t=n}while(t!==null);Vt=6,rt=null}function qd(t,e,n,a,s,r,f,m,S){t.cancelPendingCommit=null;do Ts();while(Jt!==0);if((pt&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(r=e.lanes|e.childLanes,r|=Uu,Xg(t,n,r,f,m,S),t===At&&(rt=At=null,ht=0),Ga=e,Cn=t,Xa=n,Ho=r,qo=s,Vd=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,yv(Ml,function(){return Kd(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=V.T,V.T=null,s=j.p,j.p=2,f=pt,pt|=4;try{iv(t,e,n)}finally{pt=f,j.p=s,V.T=a}}Jt=1,Yd(),Gd(),Xd()}}function Yd(){if(Jt===1){Jt=0;var t=Cn,e=Ga,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=V.T,V.T=null;var a=j.p;j.p=2;var s=pt;pt|=4;try{xd(e,t);var r=nr,f=Cf(t.containerInfo),m=r.focusedElem,S=r.selectionRange;if(f!==m&&m&&m.ownerDocument&&Of(m.ownerDocument.documentElement,m)){if(S!==null&&Ou(m)){var D=S.start,C=S.end;if(C===void 0&&(C=D),"selectionStart"in m)m.selectionStart=D,m.selectionEnd=Math.min(C,m.value.length);else{var _=m.ownerDocument||document,R=_&&_.defaultView||window;if(R.getSelection){var O=R.getSelection(),nt=m.textContent.length,I=Math.min(S.start,nt),Tt=S.end===void 0?I:Math.min(S.end,nt);!O.extend&&I>Tt&&(f=Tt,Tt=I,I=f);var E=Rf(m,I),b=Rf(m,Tt);if(E&&b&&(O.rangeCount!==1||O.anchorNode!==E.node||O.anchorOffset!==E.offset||O.focusNode!==b.node||O.focusOffset!==b.offset)){var M=_.createRange();M.setStart(E.node,E.offset),O.removeAllRanges(),I>Tt?(O.addRange(M),O.extend(b.node,b.offset)):(M.setEnd(b.node,b.offset),O.addRange(M))}}}}for(_=[],O=m;O=O.parentNode;)O.nodeType===1&&_.push({element:O,left:O.scrollLeft,top:O.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<_.length;m++){var z=_[m];z.element.scrollLeft=z.left,z.element.scrollTop=z.top}}_s=!!er,nr=er=null}finally{pt=s,j.p=a,V.T=n}}t.current=e,Jt=2}}function Gd(){if(Jt===2){Jt=0;var t=Cn,e=Ga,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=V.T,V.T=null;var a=j.p;j.p=2;var s=pt;pt|=4;try{Sd(t,e.alternate,e)}finally{pt=s,j.p=a,V.T=n}}Jt=3}}function Xd(){if(Jt===4||Jt===3){Jt=0,Ng();var t=Cn,e=Ga,n=Xa,a=Vd;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Jt=5:(Jt=0,Ga=Cn=null,Zd(t,t.pendingLanes));var s=t.pendingLanes;if(s===0&&(On=null),uu(n),e=e.stateNode,fe&&typeof fe.onCommitFiberRoot=="function")try{fe.onCommitFiberRoot(oi,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=V.T,s=j.p,j.p=2,V.T=null;try{for(var r=t.onRecoverableError,f=0;f<a.length;f++){var m=a[f];r(m.value,{componentStack:m.stack})}}finally{V.T=e,j.p=s}}(Xa&3)!==0&&Ts(),Ge(t),s=t.pendingLanes,(n&4194090)!==0&&(s&42)!==0?t===Yo?Qi++:(Qi=0,Yo=t):Qi=0,ki(0)}}function Zd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Di(e)))}function Ts(t){return Yd(),Gd(),Xd(),Kd()}function Kd(){if(Jt!==5)return!1;var t=Cn,e=Ho;Ho=0;var n=uu(Xa),a=V.T,s=j.p;try{j.p=32>n?32:n,V.T=null,n=qo,qo=null;var r=Cn,f=Xa;if(Jt=0,Ga=Cn=null,Xa=0,(pt&6)!==0)throw Error(o(331));var m=pt;if(pt|=4,Od(r.current),Md(r,r.current,f,n),pt=m,ki(0,!1),fe&&typeof fe.onPostCommitFiberRoot=="function")try{fe.onPostCommitFiberRoot(oi,r)}catch{}return!0}finally{j.p=s,V.T=a,Zd(t,e)}}function Qd(t,e,n){e=Ae(n,e),e=So(t.stateNode,e,2),t=Sn(t,e,2),t!==null&&(ci(t,2),Ge(t))}function bt(t,e,n){if(t.tag===3)Qd(t,t,n);else for(;e!==null;){if(e.tag===3){Qd(e,t,n);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(On===null||!On.has(a))){t=Ae(n,t),n=Fh(2),a=Sn(e,n,2),a!==null&&(Wh(n,a,e,t),ci(a,2),Ge(a));break}}e=e.return}}function Ko(t,e,n){var a=t.pingCache;if(a===null){a=t.pingCache=new uv;var s=new Set;a.set(e,s)}else s=a.get(e),s===void 0&&(s=new Set,a.set(e,s));s.has(n)||(Bo=!0,s.add(n),t=hv.bind(null,t,e,n),e.then(t,t))}function hv(t,e,n){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,At===t&&(ht&n)===n&&(Vt===4||Vt===3&&(ht&62914560)===ht&&300>we()-Lo?(pt&2)===0&&Za(t,0):jo|=n,Ya===ht&&(Ya=0)),Ge(t)}function kd(t,e){e===0&&(e=Gc()),t=Da(t,e),t!==null&&(ci(t,e),Ge(t))}function dv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),kd(t,n)}function mv(t,e){var n=0;switch(t.tag){case 13:var a=t.stateNode,s=t.memoizedState;s!==null&&(n=s.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(e),kd(t,n)}function yv(t,e){return au(t,e)}var bs=null,Qa=null,Qo=!1,As=!1,ko=!1,ia=0;function Ge(t){t!==Qa&&t.next===null&&(Qa===null?bs=Qa=t:Qa=Qa.next=t),As=!0,Qo||(Qo=!0,gv())}function ki(t,e){if(!ko&&As){ko=!0;do for(var n=!1,a=bs;a!==null;){if(t!==0){var s=a.pendingLanes;if(s===0)var r=0;else{var f=a.suspendedLanes,m=a.pingedLanes;r=(1<<31-he(42|t)+1)-1,r&=s&~(f&~m),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,Wd(a,r))}else r=ht,r=Ol(a,a===At?r:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(r&3)===0||ri(a,r)||(n=!0,Wd(a,r));a=a.next}while(n);ko=!1}}function pv(){Jd()}function Jd(){As=Qo=!1;var t=0;ia!==0&&(Mv()&&(t=ia),ia=0);for(var e=we(),n=null,a=bs;a!==null;){var s=a.next,r=Pd(a,e);r===0?(a.next=null,n===null?bs=s:n.next=s,s===null&&(Qa=n)):(n=a,(t!==0||(r&3)!==0)&&(As=!0)),a=s}ki(t)}function Pd(t,e){for(var n=t.suspendedLanes,a=t.pingedLanes,s=t.expirationTimes,r=t.pendingLanes&-62914561;0<r;){var f=31-he(r),m=1<<f,S=s[f];S===-1?((m&n)===0||(m&a)!==0)&&(s[f]=Gg(m,e)):S<=e&&(t.expiredLanes|=m),r&=~m}if(e=At,n=ht,n=Ol(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,n===0||t===e&&(gt===2||gt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&iu(a),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||ri(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(a!==null&&iu(a),uu(n)){case 2:case 8:n=Hc;break;case 32:n=Ml;break;case 268435456:n=qc;break;default:n=Ml}return a=Fd.bind(null,t),n=au(n,a),t.callbackPriority=e,t.callbackNode=n,e}return a!==null&&a!==null&&iu(a),t.callbackPriority=2,t.callbackNode=null,2}function Fd(t,e){if(Jt!==0&&Jt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(Ts()&&t.callbackNode!==n)return null;var a=ht;return a=Ol(t,t===At?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(_d(t,a,e),Pd(t,we()),t.callbackNode!=null&&t.callbackNode===n?Fd.bind(null,t):null)}function Wd(t,e){if(Ts())return null;_d(t,e,!0)}function gv(){Rv(function(){(pt&6)!==0?au(Lc,pv):Jd()})}function Jo(){return ia===0&&(ia=Yc()),ia}function $d(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Ul(""+t)}function Id(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function vv(t,e,n,a,s){if(e==="submit"&&n&&n.stateNode===s){var r=$d((s[ie]||null).action),f=a.submitter;f&&(e=(e=f[ie]||null)?$d(e.formAction):f.getAttribute("formAction"),e!==null&&(r=e,f=null));var m=new wl("action","action",null,a,s);t.push({event:m,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ia!==0){var S=f?Id(s,f):new FormData(s);mo(n,{pending:!0,data:S,method:s.method,action:r},null,S)}}else typeof r=="function"&&(m.preventDefault(),S=f?Id(s,f):new FormData(s),mo(n,{pending:!0,data:S,method:s.method,action:r},r,S))},currentTarget:s}]})}}for(var Po=0;Po<_u.length;Po++){var Fo=_u[Po],Sv=Fo.toLowerCase(),Tv=Fo[0].toUpperCase()+Fo.slice(1);_e(Sv,"on"+Tv)}_e(_f,"onAnimationEnd"),_e(Uf,"onAnimationIteration"),_e(Nf,"onAnimationStart"),_e("dblclick","onDoubleClick"),_e("focusin","onFocus"),_e("focusout","onBlur"),_e(w0,"onTransitionRun"),_e(L0,"onTransitionStart"),_e(H0,"onTransitionCancel"),_e(Bf,"onTransitionEnd"),pa("onMouseEnter",["mouseout","mouseover"]),pa("onMouseLeave",["mouseout","mouseover"]),pa("onPointerEnter",["pointerout","pointerover"]),pa("onPointerLeave",["pointerout","pointerover"]),Gn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Gn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Gn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Gn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Gn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Gn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ji="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),bv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ji));function tm(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var a=t[n],s=a.event;a=a.listeners;t:{var r=void 0;if(e)for(var f=a.length-1;0<=f;f--){var m=a[f],S=m.instance,D=m.currentTarget;if(m=m.listener,S!==r&&s.isPropagationStopped())break t;r=m,s.currentTarget=D;try{r(s)}catch(C){rs(C)}s.currentTarget=null,r=S}else for(f=0;f<a.length;f++){if(m=a[f],S=m.instance,D=m.currentTarget,m=m.listener,S!==r&&s.isPropagationStopped())break t;r=m,s.currentTarget=D;try{r(s)}catch(C){rs(C)}s.currentTarget=null,r=S}}}}function ct(t,e){var n=e[ou];n===void 0&&(n=e[ou]=new Set);var a=t+"__bubble";n.has(a)||(em(e,t,2,!1),n.add(a))}function Wo(t,e,n){var a=0;e&&(a|=4),em(n,t,a,e)}var xs="_reactListening"+Math.random().toString(36).slice(2);function $o(t){if(!t[xs]){t[xs]=!0,kc.forEach(function(n){n!=="selectionchange"&&(bv.has(n)||Wo(n,!1,t),Wo(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[xs]||(e[xs]=!0,Wo("selectionchange",!1,e))}}function em(t,e,n,a){switch(Mm(e)){case 2:var s=Jv;break;case 8:s=Pv;break;default:s=hr}n=s.bind(null,e,n,t),s=void 0,!Su||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),a?s!==void 0?t.addEventListener(e,n,{capture:!0,passive:s}):t.addEventListener(e,n,!0):s!==void 0?t.addEventListener(e,n,{passive:s}):t.addEventListener(e,n,!1)}function Io(t,e,n,a,s){var r=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var f=a.tag;if(f===3||f===4){var m=a.stateNode.containerInfo;if(m===s)break;if(f===4)for(f=a.return;f!==null;){var S=f.tag;if((S===3||S===4)&&f.stateNode.containerInfo===s)return;f=f.return}for(;m!==null;){if(f=da(m),f===null)return;if(S=f.tag,S===5||S===6||S===26||S===27){a=r=f;continue t}m=m.parentNode}}a=a.return}of(function(){var D=r,C=gu(n),_=[];t:{var R=jf.get(t);if(R!==void 0){var O=wl,nt=t;switch(t){case"keypress":if(Bl(n)===0)break t;case"keydown":case"keyup":O=y0;break;case"focusin":nt="focus",O=xu;break;case"focusout":nt="blur",O=xu;break;case"beforeblur":case"afterblur":O=xu;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":O=ff;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":O=a0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":O=v0;break;case _f:case Uf:case Nf:O=s0;break;case Bf:O=T0;break;case"scroll":case"scrollend":O=e0;break;case"wheel":O=A0;break;case"copy":case"cut":case"paste":O=o0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":O=df;break;case"toggle":case"beforetoggle":O=E0}var I=(e&4)!==0,Tt=!I&&(t==="scroll"||t==="scrollend"),E=I?R!==null?R+"Capture":null:R;I=[];for(var b=D,M;b!==null;){var z=b;if(M=z.stateNode,z=z.tag,z!==5&&z!==26&&z!==27||M===null||E===null||(z=di(b,E),z!=null&&I.push(Pi(b,z,M))),Tt)break;b=b.return}0<I.length&&(R=new O(R,nt,null,n,C),_.push({event:R,listeners:I}))}}if((e&7)===0){t:{if(R=t==="mouseover"||t==="pointerover",O=t==="mouseout"||t==="pointerout",R&&n!==pu&&(nt=n.relatedTarget||n.fromElement)&&(da(nt)||nt[ha]))break t;if((O||R)&&(R=C.window===C?C:(R=C.ownerDocument)?R.defaultView||R.parentWindow:window,O?(nt=n.relatedTarget||n.toElement,O=D,nt=nt?da(nt):null,nt!==null&&(Tt=d(nt),I=nt.tag,nt!==Tt||I!==5&&I!==27&&I!==6)&&(nt=null)):(O=null,nt=D),O!==nt)){if(I=ff,z="onMouseLeave",E="onMouseEnter",b="mouse",(t==="pointerout"||t==="pointerover")&&(I=df,z="onPointerLeave",E="onPointerEnter",b="pointer"),Tt=O==null?R:hi(O),M=nt==null?R:hi(nt),R=new I(z,b+"leave",O,n,C),R.target=Tt,R.relatedTarget=M,z=null,da(C)===D&&(I=new I(E,b+"enter",nt,n,C),I.target=M,I.relatedTarget=Tt,z=I),Tt=z,O&&nt)e:{for(I=O,E=nt,b=0,M=I;M;M=ka(M))b++;for(M=0,z=E;z;z=ka(z))M++;for(;0<b-M;)I=ka(I),b--;for(;0<M-b;)E=ka(E),M--;for(;b--;){if(I===E||E!==null&&I===E.alternate)break e;I=ka(I),E=ka(E)}I=null}else I=null;O!==null&&nm(_,R,O,I,!1),nt!==null&&Tt!==null&&nm(_,Tt,nt,I,!0)}}t:{if(R=D?hi(D):window,O=R.nodeName&&R.nodeName.toLowerCase(),O==="select"||O==="input"&&R.type==="file")var K=bf;else if(Sf(R))if(Af)K=N0;else{K=_0;var ot=z0}else O=R.nodeName,!O||O.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?D&&yu(D.elementType)&&(K=bf):K=U0;if(K&&(K=K(t,D))){Tf(_,K,n,C);break t}ot&&ot(t,R,D),t==="focusout"&&D&&R.type==="number"&&D.memoizedProps.value!=null&&mu(R,"number",R.value)}switch(ot=D?hi(D):window,t){case"focusin":(Sf(ot)||ot.contentEditable==="true")&&(xa=ot,Cu=D,bi=null);break;case"focusout":bi=Cu=xa=null;break;case"mousedown":Vu=!0;break;case"contextmenu":case"mouseup":case"dragend":Vu=!1,Vf(_,n,C);break;case"selectionchange":if(j0)break;case"keydown":case"keyup":Vf(_,n,C)}var P;if(Mu)t:{switch(t){case"compositionstart":var et="onCompositionStart";break t;case"compositionend":et="onCompositionEnd";break t;case"compositionupdate":et="onCompositionUpdate";break t}et=void 0}else Aa?gf(t,n)&&(et="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(et="onCompositionStart");et&&(mf&&n.locale!=="ko"&&(Aa||et!=="onCompositionStart"?et==="onCompositionEnd"&&Aa&&(P=rf()):(yn=C,Tu="value"in yn?yn.value:yn.textContent,Aa=!0)),ot=Es(D,et),0<ot.length&&(et=new hf(et,t,null,n,C),_.push({event:et,listeners:ot}),P?et.data=P:(P=vf(n),P!==null&&(et.data=P)))),(P=D0?R0(t,n):O0(t,n))&&(et=Es(D,"onBeforeInput"),0<et.length&&(ot=new hf("onBeforeInput","beforeinput",null,n,C),_.push({event:ot,listeners:et}),ot.data=P)),vv(_,t,D,n,C)}tm(_,e)})}function Pi(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Es(t,e){for(var n=e+"Capture",a=[];t!==null;){var s=t,r=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||r===null||(s=di(t,n),s!=null&&a.unshift(Pi(t,s,r)),s=di(t,e),s!=null&&a.push(Pi(t,s,r))),t.tag===3)return a;t=t.return}return[]}function ka(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function nm(t,e,n,a,s){for(var r=e._reactName,f=[];n!==null&&n!==a;){var m=n,S=m.alternate,D=m.stateNode;if(m=m.tag,S!==null&&S===a)break;m!==5&&m!==26&&m!==27||D===null||(S=D,s?(D=di(n,r),D!=null&&f.unshift(Pi(n,D,S))):s||(D=di(n,r),D!=null&&f.push(Pi(n,D,S)))),n=n.return}f.length!==0&&t.push({event:e,listeners:f})}var Av=/\r\n?/g,xv=/\u0000|\uFFFD/g;function am(t){return(typeof t=="string"?t:""+t).replace(Av,`
`).replace(xv,"")}function im(t,e){return e=am(e),am(t)===e}function Ms(){}function St(t,e,n,a,s,r){switch(n){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||Sa(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&Sa(t,""+a);break;case"className":Vl(t,"class",a);break;case"tabIndex":Vl(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Vl(t,n,a);break;case"style":sf(t,a,r);break;case"data":if(e!=="object"){Vl(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(n);break}a=Ul(""+a),t.setAttribute(n,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(e!=="input"&&St(t,e,"name",s.name,s,null),St(t,e,"formEncType",s.formEncType,s,null),St(t,e,"formMethod",s.formMethod,s,null),St(t,e,"formTarget",s.formTarget,s,null)):(St(t,e,"encType",s.encType,s,null),St(t,e,"method",s.method,s,null),St(t,e,"target",s.target,s,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(n);break}a=Ul(""+a),t.setAttribute(n,a);break;case"onClick":a!=null&&(t.onclick=Ms);break;case"onScroll":a!=null&&ct("scroll",t);break;case"onScrollEnd":a!=null&&ct("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(n=a.__html,n!=null){if(s.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}n=Ul(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(n,""+a):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":a===!0?t.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(n,a):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(n,a):t.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(n):t.setAttribute(n,a);break;case"popover":ct("beforetoggle",t),ct("toggle",t),Cl(t,"popover",a);break;case"xlinkActuate":ke(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":ke(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":ke(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":ke(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":ke(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":ke(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":ke(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":ke(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":ke(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Cl(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Ig.get(n)||n,Cl(t,n,a))}}function tr(t,e,n,a,s,r){switch(n){case"style":sf(t,a,r);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(n=a.__html,n!=null){if(s.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"children":typeof a=="string"?Sa(t,a):(typeof a=="number"||typeof a=="bigint")&&Sa(t,""+a);break;case"onScroll":a!=null&&ct("scroll",t);break;case"onScrollEnd":a!=null&&ct("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Ms);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Jc.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(s=n.endsWith("Capture"),e=n.slice(2,s?n.length-7:void 0),r=t[ie]||null,r=r!=null?r[n]:null,typeof r=="function"&&t.removeEventListener(e,r,s),typeof a=="function")){typeof r!="function"&&r!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,a,s);break t}n in t?t[n]=a:a===!0?t.setAttribute(n,""):Cl(t,n,a)}}}function Pt(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ct("error",t),ct("load",t);var a=!1,s=!1,r;for(r in n)if(n.hasOwnProperty(r)){var f=n[r];if(f!=null)switch(r){case"src":a=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:St(t,e,r,f,n,null)}}s&&St(t,e,"srcSet",n.srcSet,n,null),a&&St(t,e,"src",n.src,n,null);return;case"input":ct("invalid",t);var m=r=f=s=null,S=null,D=null;for(a in n)if(n.hasOwnProperty(a)){var C=n[a];if(C!=null)switch(a){case"name":s=C;break;case"type":f=C;break;case"checked":S=C;break;case"defaultChecked":D=C;break;case"value":r=C;break;case"defaultValue":m=C;break;case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(o(137,e));break;default:St(t,e,a,C,n,null)}}ef(t,r,m,S,D,f,s,!1),zl(t);return;case"select":ct("invalid",t),a=f=r=null;for(s in n)if(n.hasOwnProperty(s)&&(m=n[s],m!=null))switch(s){case"value":r=m;break;case"defaultValue":f=m;break;case"multiple":a=m;default:St(t,e,s,m,n,null)}e=r,n=f,t.multiple=!!a,e!=null?va(t,!!a,e,!1):n!=null&&va(t,!!a,n,!0);return;case"textarea":ct("invalid",t),r=s=a=null;for(f in n)if(n.hasOwnProperty(f)&&(m=n[f],m!=null))switch(f){case"value":a=m;break;case"defaultValue":s=m;break;case"children":r=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(o(91));break;default:St(t,e,f,m,n,null)}af(t,a,s,r),zl(t);return;case"option":for(S in n)if(n.hasOwnProperty(S)&&(a=n[S],a!=null))switch(S){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:St(t,e,S,a,n,null)}return;case"dialog":ct("beforetoggle",t),ct("toggle",t),ct("cancel",t),ct("close",t);break;case"iframe":case"object":ct("load",t);break;case"video":case"audio":for(a=0;a<Ji.length;a++)ct(Ji[a],t);break;case"image":ct("error",t),ct("load",t);break;case"details":ct("toggle",t);break;case"embed":case"source":case"link":ct("error",t),ct("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in n)if(n.hasOwnProperty(D)&&(a=n[D],a!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:St(t,e,D,a,n,null)}return;default:if(yu(e)){for(C in n)n.hasOwnProperty(C)&&(a=n[C],a!==void 0&&tr(t,e,C,a,n,void 0));return}}for(m in n)n.hasOwnProperty(m)&&(a=n[m],a!=null&&St(t,e,m,a,n,null))}function Ev(t,e,n,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,r=null,f=null,m=null,S=null,D=null,C=null;for(O in n){var _=n[O];if(n.hasOwnProperty(O)&&_!=null)switch(O){case"checked":break;case"value":break;case"defaultValue":S=_;default:a.hasOwnProperty(O)||St(t,e,O,null,a,_)}}for(var R in a){var O=a[R];if(_=n[R],a.hasOwnProperty(R)&&(O!=null||_!=null))switch(R){case"type":r=O;break;case"name":s=O;break;case"checked":D=O;break;case"defaultChecked":C=O;break;case"value":f=O;break;case"defaultValue":m=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(o(137,e));break;default:O!==_&&St(t,e,R,O,a,_)}}du(t,f,m,S,D,C,r,s);return;case"select":O=f=m=R=null;for(r in n)if(S=n[r],n.hasOwnProperty(r)&&S!=null)switch(r){case"value":break;case"multiple":O=S;default:a.hasOwnProperty(r)||St(t,e,r,null,a,S)}for(s in a)if(r=a[s],S=n[s],a.hasOwnProperty(s)&&(r!=null||S!=null))switch(s){case"value":R=r;break;case"defaultValue":m=r;break;case"multiple":f=r;default:r!==S&&St(t,e,s,r,a,S)}e=m,n=f,a=O,R!=null?va(t,!!n,R,!1):!!a!=!!n&&(e!=null?va(t,!!n,e,!0):va(t,!!n,n?[]:"",!1));return;case"textarea":O=R=null;for(m in n)if(s=n[m],n.hasOwnProperty(m)&&s!=null&&!a.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:St(t,e,m,null,a,s)}for(f in a)if(s=a[f],r=n[f],a.hasOwnProperty(f)&&(s!=null||r!=null))switch(f){case"value":R=s;break;case"defaultValue":O=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(o(91));break;default:s!==r&&St(t,e,f,s,a,r)}nf(t,R,O);return;case"option":for(var nt in n)if(R=n[nt],n.hasOwnProperty(nt)&&R!=null&&!a.hasOwnProperty(nt))switch(nt){case"selected":t.selected=!1;break;default:St(t,e,nt,null,a,R)}for(S in a)if(R=a[S],O=n[S],a.hasOwnProperty(S)&&R!==O&&(R!=null||O!=null))switch(S){case"selected":t.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:St(t,e,S,R,a,O)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var I in n)R=n[I],n.hasOwnProperty(I)&&R!=null&&!a.hasOwnProperty(I)&&St(t,e,I,null,a,R);for(D in a)if(R=a[D],O=n[D],a.hasOwnProperty(D)&&R!==O&&(R!=null||O!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(o(137,e));break;default:St(t,e,D,R,a,O)}return;default:if(yu(e)){for(var Tt in n)R=n[Tt],n.hasOwnProperty(Tt)&&R!==void 0&&!a.hasOwnProperty(Tt)&&tr(t,e,Tt,void 0,a,R);for(C in a)R=a[C],O=n[C],!a.hasOwnProperty(C)||R===O||R===void 0&&O===void 0||tr(t,e,C,R,a,O);return}}for(var E in n)R=n[E],n.hasOwnProperty(E)&&R!=null&&!a.hasOwnProperty(E)&&St(t,e,E,null,a,R);for(_ in a)R=a[_],O=n[_],!a.hasOwnProperty(_)||R===O||R==null&&O==null||St(t,e,_,R,a,O)}var er=null,nr=null;function Ds(t){return t.nodeType===9?t:t.ownerDocument}function lm(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function sm(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function ar(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ir=null;function Mv(){var t=window.event;return t&&t.type==="popstate"?t===ir?!1:(ir=t,!0):(ir=null,!1)}var um=typeof setTimeout=="function"?setTimeout:void 0,Dv=typeof clearTimeout=="function"?clearTimeout:void 0,om=typeof Promise=="function"?Promise:void 0,Rv=typeof queueMicrotask=="function"?queueMicrotask:typeof om<"u"?function(t){return om.resolve(null).then(t).catch(Ov)}:um;function Ov(t){setTimeout(function(){throw t})}function zn(t){return t==="head"}function rm(t,e){var n=e,a=0,s=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<a&&8>a){n=a;var f=t.ownerDocument;if(n&1&&Fi(f.documentElement),n&2&&Fi(f.body),n&4)for(n=f.head,Fi(n),f=n.firstChild;f;){var m=f.nextSibling,S=f.nodeName;f[fi]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=m}}if(s===0){t.removeChild(r),il(e);return}s--}else n==="$"||n==="$?"||n==="$!"?s++:a=n.charCodeAt(0)-48;else a=0;n=r}while(n);il(e)}function lr(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":lr(n),ru(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function Cv(t,e,n,a){for(;t.nodeType===1;){var s=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[fi])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(r=t.getAttribute("rel"),r==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(r!==s.rel||t.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||t.getAttribute("title")!==(s.title==null?null:s.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(r=t.getAttribute("src"),(r!==(s.src==null?null:s.src)||t.getAttribute("type")!==(s.type==null?null:s.type)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&r&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var r=s.name==null?null:""+s.name;if(s.type==="hidden"&&t.getAttribute("name")===r)return t}else return t;if(t=Ne(t.nextSibling),t===null)break}return null}function Vv(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Ne(t.nextSibling),t===null))return null;return t}function sr(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function zv(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var a=function(){e(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Ne(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var ur=null;function cm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function fm(t,e,n){switch(e=Ds(n),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function Fi(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);ru(t)}var Oe=new Map,hm=new Set;function Rs(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var on=j.d;j.d={f:_v,r:Uv,D:Nv,C:Bv,L:jv,m:wv,X:Hv,S:Lv,M:qv};function _v(){var t=on.f(),e=vs();return t||e}function Uv(t){var e=ma(t);e!==null&&e.tag===5&&e.type==="form"?_h(e):on.r(t)}var Ja=typeof document>"u"?null:document;function dm(t,e,n){var a=Ja;if(a&&typeof e=="string"&&e){var s=be(e);s='link[rel="'+t+'"][href="'+s+'"]',typeof n=="string"&&(s+='[crossorigin="'+n+'"]'),hm.has(s)||(hm.add(s),t={rel:t,crossOrigin:n,href:e},a.querySelector(s)===null&&(e=a.createElement("link"),Pt(e,"link",t),Xt(e),a.head.appendChild(e)))}}function Nv(t){on.D(t),dm("dns-prefetch",t,null)}function Bv(t,e){on.C(t,e),dm("preconnect",t,e)}function jv(t,e,n){on.L(t,e,n);var a=Ja;if(a&&t&&e){var s='link[rel="preload"][as="'+be(e)+'"]';e==="image"&&n&&n.imageSrcSet?(s+='[imagesrcset="'+be(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(s+='[imagesizes="'+be(n.imageSizes)+'"]')):s+='[href="'+be(t)+'"]';var r=s;switch(e){case"style":r=Pa(t);break;case"script":r=Fa(t)}Oe.has(r)||(t=v({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Oe.set(r,t),a.querySelector(s)!==null||e==="style"&&a.querySelector(Wi(r))||e==="script"&&a.querySelector($i(r))||(e=a.createElement("link"),Pt(e,"link",t),Xt(e),a.head.appendChild(e)))}}function wv(t,e){on.m(t,e);var n=Ja;if(n&&t){var a=e&&typeof e.as=="string"?e.as:"script",s='link[rel="modulepreload"][as="'+be(a)+'"][href="'+be(t)+'"]',r=s;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=Fa(t)}if(!Oe.has(r)&&(t=v({rel:"modulepreload",href:t},e),Oe.set(r,t),n.querySelector(s)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector($i(r)))return}a=n.createElement("link"),Pt(a,"link",t),Xt(a),n.head.appendChild(a)}}}function Lv(t,e,n){on.S(t,e,n);var a=Ja;if(a&&t){var s=ya(a).hoistableStyles,r=Pa(t);e=e||"default";var f=s.get(r);if(!f){var m={loading:0,preload:null};if(f=a.querySelector(Wi(r)))m.loading=5;else{t=v({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Oe.get(r))&&or(t,n);var S=f=a.createElement("link");Xt(S),Pt(S,"link",t),S._p=new Promise(function(D,C){S.onload=D,S.onerror=C}),S.addEventListener("load",function(){m.loading|=1}),S.addEventListener("error",function(){m.loading|=2}),m.loading|=4,Os(f,e,a)}f={type:"stylesheet",instance:f,count:1,state:m},s.set(r,f)}}}function Hv(t,e){on.X(t,e);var n=Ja;if(n&&t){var a=ya(n).hoistableScripts,s=Fa(t),r=a.get(s);r||(r=n.querySelector($i(s)),r||(t=v({src:t,async:!0},e),(e=Oe.get(s))&&rr(t,e),r=n.createElement("script"),Xt(r),Pt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},a.set(s,r))}}function qv(t,e){on.M(t,e);var n=Ja;if(n&&t){var a=ya(n).hoistableScripts,s=Fa(t),r=a.get(s);r||(r=n.querySelector($i(s)),r||(t=v({src:t,async:!0,type:"module"},e),(e=Oe.get(s))&&rr(t,e),r=n.createElement("script"),Xt(r),Pt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},a.set(s,r))}}function mm(t,e,n,a){var s=(s=lt.current)?Rs(s):null;if(!s)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Pa(n.href),n=ya(s).hoistableStyles,a=n.get(e),a||(a={type:"style",instance:null,count:0,state:null},n.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Pa(n.href);var r=ya(s).hoistableStyles,f=r.get(t);if(f||(s=s.ownerDocument||s,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(t,f),(r=s.querySelector(Wi(t)))&&!r._p&&(f.instance=r,f.state.loading=5),Oe.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Oe.set(t,n),r||Yv(s,t,n,f.state))),e&&a===null)throw Error(o(528,""));return f}if(e&&a!==null)throw Error(o(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Fa(n),n=ya(s).hoistableScripts,a=n.get(e),a||(a={type:"script",instance:null,count:0,state:null},n.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function Pa(t){return'href="'+be(t)+'"'}function Wi(t){return'link[rel="stylesheet"]['+t+"]"}function ym(t){return v({},t,{"data-precedence":t.precedence,precedence:null})}function Yv(t,e,n,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),Pt(e,"link",n),Xt(e),t.head.appendChild(e))}function Fa(t){return'[src="'+be(t)+'"]'}function $i(t){return"script[async]"+t}function pm(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+be(n.href)+'"]');if(a)return e.instance=a,Xt(a),a;var s=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Xt(a),Pt(a,"style",s),Os(a,n.precedence,t),e.instance=a;case"stylesheet":s=Pa(n.href);var r=t.querySelector(Wi(s));if(r)return e.state.loading|=4,e.instance=r,Xt(r),r;a=ym(n),(s=Oe.get(s))&&or(a,s),r=(t.ownerDocument||t).createElement("link"),Xt(r);var f=r;return f._p=new Promise(function(m,S){f.onload=m,f.onerror=S}),Pt(r,"link",a),e.state.loading|=4,Os(r,n.precedence,t),e.instance=r;case"script":return r=Fa(n.src),(s=t.querySelector($i(r)))?(e.instance=s,Xt(s),s):(a=n,(s=Oe.get(r))&&(a=v({},n),rr(a,s)),t=t.ownerDocument||t,s=t.createElement("script"),Xt(s),Pt(s,"link",a),t.head.appendChild(s),e.instance=s);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Os(a,n.precedence,t));return e.instance}function Os(t,e,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=a.length?a[a.length-1]:null,r=s,f=0;f<a.length;f++){var m=a[f];if(m.dataset.precedence===e)r=m;else if(r!==s)break}r?r.parentNode.insertBefore(t,r.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function or(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function rr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Cs=null;function gm(t,e,n){if(Cs===null){var a=new Map,s=Cs=new Map;s.set(n,a)}else s=Cs,a=s.get(n),a||(a=new Map,s.set(n,a));if(a.has(t))return a;for(a.set(t,null),n=n.getElementsByTagName(t),s=0;s<n.length;s++){var r=n[s];if(!(r[fi]||r[$t]||t==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var f=r.getAttribute(e)||"";f=t+f;var m=a.get(f);m?m.push(r):a.set(f,[r])}}return a}function vm(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Gv(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Sm(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Ii=null;function Xv(){}function Zv(t,e,n){if(Ii===null)throw Error(o(475));var a=Ii;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var s=Pa(n.href),r=t.querySelector(Wi(s));if(r){t=r._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Vs.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=r,Xt(r);return}r=t.ownerDocument||t,n=ym(n),(s=Oe.get(s))&&or(n,s),r=r.createElement("link"),Xt(r);var f=r;f._p=new Promise(function(m,S){f.onload=m,f.onerror=S}),Pt(r,"link",n),e.instance=r}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Vs.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function Kv(){if(Ii===null)throw Error(o(475));var t=Ii;return t.stylesheets&&t.count===0&&cr(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&cr(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Vs(){if(this.count--,this.count===0){if(this.stylesheets)cr(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var zs=null;function cr(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,zs=new Map,e.forEach(Qv,t),zs=null,Vs.call(t))}function Qv(t,e){if(!(e.state.loading&4)){var n=zs.get(t);if(n)var a=n.get(null);else{n=new Map,zs.set(t,n);for(var s=t.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<s.length;r++){var f=s[r];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),a=f)}a&&n.set(null,a)}s=e.instance,f=s.getAttribute("data-precedence"),r=n.get(f)||a,r===a&&n.set(null,s),n.set(f,s),this.count++,a=Vs.bind(this),s.addEventListener("load",a),s.addEventListener("error",a),r?r.parentNode.insertBefore(s,r.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(s,t.firstChild)),e.state.loading|=4}}var tl={$$typeof:H,Provider:null,Consumer:null,_currentValue:k,_currentValue2:k,_threadCount:0};function kv(t,e,n,a,s,r,f,m){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=lu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=lu(0),this.hiddenUpdates=lu(null),this.identifierPrefix=a,this.onUncaughtError=s,this.onCaughtError=r,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function Tm(t,e,n,a,s,r,f,m,S,D,C,_){return t=new kv(t,e,n,f,m,S,D,_),e=1,r===!0&&(e|=24),r=me(3,null,null,e),t.current=r,r.stateNode=t,e=Zu(),e.refCount++,t.pooledCache=e,e.refCount++,r.memoizedState={element:a,isDehydrated:n,cache:e},Ju(r),t}function bm(t){return t?(t=Ra,t):Ra}function Am(t,e,n,a,s,r){s=bm(s),a.context===null?a.context=s:a.pendingContext=s,a=vn(e),a.payload={element:n},r=r===void 0?null:r,r!==null&&(a.callback=r),n=Sn(t,a,e),n!==null&&(Se(n,t,e),Vi(n,t,e))}function xm(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function fr(t,e){xm(t,e),(t=t.alternate)&&xm(t,e)}function Em(t){if(t.tag===13){var e=Da(t,67108864);e!==null&&Se(e,t,67108864),fr(t,67108864)}}var _s=!0;function Jv(t,e,n,a){var s=V.T;V.T=null;var r=j.p;try{j.p=2,hr(t,e,n,a)}finally{j.p=r,V.T=s}}function Pv(t,e,n,a){var s=V.T;V.T=null;var r=j.p;try{j.p=8,hr(t,e,n,a)}finally{j.p=r,V.T=s}}function hr(t,e,n,a){if(_s){var s=dr(a);if(s===null)Io(t,e,a,Us,n),Dm(t,a);else if(Wv(s,t,e,n,a))a.stopPropagation();else if(Dm(t,a),e&4&&-1<Fv.indexOf(t)){for(;s!==null;){var r=ma(s);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var f=Yn(r.pendingLanes);if(f!==0){var m=r;for(m.pendingLanes|=2,m.entangledLanes|=2;f;){var S=1<<31-he(f);m.entanglements[1]|=S,f&=~S}Ge(r),(pt&6)===0&&(ps=we()+500,ki(0))}}break;case 13:m=Da(r,2),m!==null&&Se(m,r,2),vs(),fr(r,2)}if(r=dr(a),r===null&&Io(t,e,a,Us,n),r===s)break;s=r}s!==null&&a.stopPropagation()}else Io(t,e,a,null,n)}}function dr(t){return t=gu(t),mr(t)}var Us=null;function mr(t){if(Us=null,t=da(t),t!==null){var e=d(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=h(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Us=t,null}function Mm(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Bg()){case Lc:return 2;case Hc:return 8;case Ml:case jg:return 32;case qc:return 268435456;default:return 32}default:return 32}}var yr=!1,_n=null,Un=null,Nn=null,el=new Map,nl=new Map,Bn=[],Fv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Dm(t,e){switch(t){case"focusin":case"focusout":_n=null;break;case"dragenter":case"dragleave":Un=null;break;case"mouseover":case"mouseout":Nn=null;break;case"pointerover":case"pointerout":el.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":nl.delete(e.pointerId)}}function al(t,e,n,a,s,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:a,nativeEvent:r,targetContainers:[s]},e!==null&&(e=ma(e),e!==null&&Em(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function Wv(t,e,n,a,s){switch(e){case"focusin":return _n=al(_n,t,e,n,a,s),!0;case"dragenter":return Un=al(Un,t,e,n,a,s),!0;case"mouseover":return Nn=al(Nn,t,e,n,a,s),!0;case"pointerover":var r=s.pointerId;return el.set(r,al(el.get(r)||null,t,e,n,a,s)),!0;case"gotpointercapture":return r=s.pointerId,nl.set(r,al(nl.get(r)||null,t,e,n,a,s)),!0}return!1}function Rm(t){var e=da(t.target);if(e!==null){var n=d(e);if(n!==null){if(e=n.tag,e===13){if(e=h(n),e!==null){t.blockedOn=e,Zg(t.priority,function(){if(n.tag===13){var a=ve();a=su(a);var s=Da(n,a);s!==null&&Se(s,n,a),fr(n,a)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Ns(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=dr(t.nativeEvent);if(n===null){n=t.nativeEvent;var a=new n.constructor(n.type,n);pu=a,n.target.dispatchEvent(a),pu=null}else return e=ma(n),e!==null&&Em(e),t.blockedOn=n,!1;e.shift()}return!0}function Om(t,e,n){Ns(t)&&n.delete(e)}function $v(){yr=!1,_n!==null&&Ns(_n)&&(_n=null),Un!==null&&Ns(Un)&&(Un=null),Nn!==null&&Ns(Nn)&&(Nn=null),el.forEach(Om),nl.forEach(Om)}function Bs(t,e){t.blockedOn===e&&(t.blockedOn=null,yr||(yr=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,$v)))}var js=null;function Cm(t){js!==t&&(js=t,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){js===t&&(js=null);for(var e=0;e<t.length;e+=3){var n=t[e],a=t[e+1],s=t[e+2];if(typeof a!="function"){if(mr(a||n)===null)continue;break}var r=ma(n);r!==null&&(t.splice(e,3),e-=3,mo(r,{pending:!0,data:s,method:n.method,action:a},a,s))}}))}function il(t){function e(S){return Bs(S,t)}_n!==null&&Bs(_n,t),Un!==null&&Bs(Un,t),Nn!==null&&Bs(Nn,t),el.forEach(e),nl.forEach(e);for(var n=0;n<Bn.length;n++){var a=Bn[n];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Bn.length&&(n=Bn[0],n.blockedOn===null);)Rm(n),n.blockedOn===null&&Bn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var s=n[a],r=n[a+1],f=s[ie]||null;if(typeof r=="function")f||Cm(n);else if(f){var m=null;if(r&&r.hasAttribute("formAction")){if(s=r,f=r[ie]||null)m=f.formAction;else if(mr(s)!==null)continue}else m=f.action;typeof m=="function"?n[a+1]=m:(n.splice(a,3),a-=3),Cm(n)}}}function pr(t){this._internalRoot=t}ws.prototype.render=pr.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var n=e.current,a=ve();Am(n,a,t,e,null,null)},ws.prototype.unmount=pr.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Am(t.current,2,null,t,null,null),vs(),e[ha]=null}};function ws(t){this._internalRoot=t}ws.prototype.unstable_scheduleHydration=function(t){if(t){var e=Kc();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Bn.length&&e!==0&&e<Bn[n].priority;n++);Bn.splice(n,0,t),n===0&&Rm(t)}};var Vm=l.version;if(Vm!=="19.1.1")throw Error(o(527,Vm,"19.1.1"));j.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=g(e),t=t!==null?y(t):null,t=t===null?null:t.stateNode,t};var Iv={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:V,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ls=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ls.isDisabled&&Ls.supportsFiber)try{oi=Ls.inject(Iv),fe=Ls}catch{}}return sl.createRoot=function(t,e){if(!c(t))throw Error(o(299));var n=!1,a="",s=Qh,r=kh,f=Jh,m=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(s=e.onUncaughtError),e.onCaughtError!==void 0&&(r=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(m=e.unstable_transitionCallbacks)),e=Tm(t,1,!1,null,null,n,a,s,r,f,m,null),t[ha]=e.current,$o(t),new pr(e)},sl.hydrateRoot=function(t,e,n){if(!c(t))throw Error(o(299));var a=!1,s="",r=Qh,f=kh,m=Jh,S=null,D=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(m=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(S=n.unstable_transitionCallbacks),n.formState!==void 0&&(D=n.formState)),e=Tm(t,1,!0,e,n??null,a,s,r,f,m,S,D),e.context=bm(null),n=e.current,a=ve(),a=su(a),s=vn(a),s.callback=null,Sn(n,s,a),n=a,e.current.lanes=n,ci(e,n),Ge(e),t[ha]=e.current,$o(t),new ws(e)},sl.version="19.1.1",sl}var qm;function r1(){if(qm)return Sr.exports;qm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(l){console.error(l)}}return i(),Sr.exports=o1(),Sr.exports}var c1=r1();const ep=W.createContext({});function f1(i){const l=W.useRef(null);return l.current===null&&(l.current=i()),l.current}const sc=typeof window<"u",h1=sc?W.useLayoutEffect:W.useEffect,uc=W.createContext(null);function oc(i,l){i.indexOf(l)===-1&&i.push(l)}function rc(i,l){const u=i.indexOf(l);u>-1&&i.splice(u,1)}const cn=(i,l,u)=>u>l?l:u<i?i:u;let cc=()=>{};const fn={},np=i=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(i);function ap(i){return typeof i=="object"&&i!==null}const ip=i=>/^0[^.\s]+$/u.test(i);function fc(i){let l;return()=>(l===void 0&&(l=i()),l)}const ze=i=>i,d1=(i,l)=>u=>l(i(u)),Tl=(...i)=>i.reduce(d1),ml=(i,l,u)=>{const o=l-i;return o===0?1:(u-i)/o};class hc{constructor(){this.subscriptions=[]}add(l){return oc(this.subscriptions,l),()=>rc(this.subscriptions,l)}notify(l,u,o){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](l,u,o);else for(let d=0;d<c;d++){const h=this.subscriptions[d];h&&h(l,u,o)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Xe=i=>i*1e3,Ze=i=>i/1e3;function lp(i,l){return l?i*(1e3/l):0}const sp=(i,l,u)=>(((1-3*u+3*l)*i+(3*u-6*l))*i+3*l)*i,m1=1e-7,y1=12;function p1(i,l,u,o,c){let d,h,p=0;do h=l+(u-l)/2,d=sp(h,o,c)-i,d>0?u=h:l=h;while(Math.abs(d)>m1&&++p<y1);return h}function bl(i,l,u,o){if(i===l&&u===o)return ze;const c=d=>p1(d,0,1,i,u);return d=>d===0||d===1?d:sp(c(d),l,o)}const up=i=>l=>l<=.5?i(2*l)/2:(2-i(2*(1-l)))/2,op=i=>l=>1-i(1-l),rp=bl(.33,1.53,.69,.99),dc=op(rp),cp=up(dc),fp=i=>(i*=2)<1?.5*dc(i):.5*(2-Math.pow(2,-10*(i-1))),mc=i=>1-Math.sin(Math.acos(i)),hp=op(mc),dp=up(mc),g1=bl(.42,0,1,1),v1=bl(0,0,.58,1),mp=bl(.42,0,.58,1),S1=i=>Array.isArray(i)&&typeof i[0]!="number",yp=i=>Array.isArray(i)&&typeof i[0]=="number",T1={linear:ze,easeIn:g1,easeInOut:mp,easeOut:v1,circIn:mc,circInOut:dp,circOut:hp,backIn:dc,backInOut:cp,backOut:rp,anticipate:fp},b1=i=>typeof i=="string",Ym=i=>{if(yp(i)){cc(i.length===4);const[l,u,o,c]=i;return bl(l,u,o,c)}else if(b1(i))return T1[i];return i},Hs=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function A1(i,l){let u=new Set,o=new Set,c=!1,d=!1;const h=new WeakSet;let p={delta:0,timestamp:0,isProcessing:!1};function g(v){h.has(v)&&(y.schedule(v),i()),v(p)}const y={schedule:(v,A=!1,x=!1)=>{const w=x&&c?u:o;return A&&h.add(v),w.has(v)||w.add(v),v},cancel:v=>{o.delete(v),h.delete(v)},process:v=>{if(p=v,c){d=!0;return}c=!0,[u,o]=[o,u],u.forEach(g),u.clear(),c=!1,d&&(d=!1,y.process(v))}};return y}const x1=40;function pp(i,l){let u=!1,o=!0;const c={delta:0,timestamp:0,isProcessing:!1},d=()=>u=!0,h=Hs.reduce((H,Q)=>(H[Q]=A1(d),H),{}),{setup:p,read:g,resolveKeyframes:y,preUpdate:v,update:A,preRender:x,render:U,postRender:w}=h,L=()=>{const H=fn.useManualTiming?c.timestamp:performance.now();u=!1,fn.useManualTiming||(c.delta=o?1e3/60:Math.max(Math.min(H-c.timestamp,x1),1)),c.timestamp=H,c.isProcessing=!0,p.process(c),g.process(c),y.process(c),v.process(c),A.process(c),x.process(c),U.process(c),w.process(c),c.isProcessing=!1,u&&l&&(o=!1,i(L))},Y=()=>{u=!0,o=!0,c.isProcessing||i(L)};return{schedule:Hs.reduce((H,Q)=>{const B=h[Q];return H[Q]=(tt,it=!1,F=!1)=>(u||Y(),B.schedule(tt,it,F)),H},{}),cancel:H=>{for(let Q=0;Q<Hs.length;Q++)h[Hs[Q]].cancel(H)},state:c,steps:h}}const{schedule:Dt,cancel:Ln,state:Ft,steps:xr}=pp(typeof requestAnimationFrame<"u"?requestAnimationFrame:ze,!0);let Xs;function E1(){Xs=void 0}const re={now:()=>(Xs===void 0&&re.set(Ft.isProcessing||fn.useManualTiming?Ft.timestamp:performance.now()),Xs),set:i=>{Xs=i,queueMicrotask(E1)}},gp=i=>l=>typeof l=="string"&&l.startsWith(i),yc=gp("--"),M1=gp("var(--"),pc=i=>M1(i)?D1.test(i.split("/*")[0].trim()):!1,D1=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,li={test:i=>typeof i=="number",parse:parseFloat,transform:i=>i},yl={...li,transform:i=>cn(0,1,i)},qs={...li,default:1},rl=i=>Math.round(i*1e5)/1e5,gc=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function R1(i){return i==null}const O1=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,vc=(i,l)=>u=>!!(typeof u=="string"&&O1.test(u)&&u.startsWith(i)||l&&!R1(u)&&Object.prototype.hasOwnProperty.call(u,l)),vp=(i,l,u)=>o=>{if(typeof o!="string")return o;const[c,d,h,p]=o.match(gc);return{[i]:parseFloat(c),[l]:parseFloat(d),[u]:parseFloat(h),alpha:p!==void 0?parseFloat(p):1}},C1=i=>cn(0,255,i),Er={...li,transform:i=>Math.round(C1(i))},ua={test:vc("rgb","red"),parse:vp("red","green","blue"),transform:({red:i,green:l,blue:u,alpha:o=1})=>"rgba("+Er.transform(i)+", "+Er.transform(l)+", "+Er.transform(u)+", "+rl(yl.transform(o))+")"};function V1(i){let l="",u="",o="",c="";return i.length>5?(l=i.substring(1,3),u=i.substring(3,5),o=i.substring(5,7),c=i.substring(7,9)):(l=i.substring(1,2),u=i.substring(2,3),o=i.substring(3,4),c=i.substring(4,5),l+=l,u+=u,o+=o,c+=c),{red:parseInt(l,16),green:parseInt(u,16),blue:parseInt(o,16),alpha:c?parseInt(c,16)/255:1}}const Lr={test:vc("#"),parse:V1,transform:ua.transform},Al=i=>({test:l=>typeof l=="string"&&l.endsWith(i)&&l.split(" ").length===1,parse:parseFloat,transform:l=>`${l}${i}`}),wn=Al("deg"),Ke=Al("%"),at=Al("px"),z1=Al("vh"),_1=Al("vw"),Gm={...Ke,parse:i=>Ke.parse(i)/100,transform:i=>Ke.transform(i*100)},Wa={test:vc("hsl","hue"),parse:vp("hue","saturation","lightness"),transform:({hue:i,saturation:l,lightness:u,alpha:o=1})=>"hsla("+Math.round(i)+", "+Ke.transform(rl(l))+", "+Ke.transform(rl(u))+", "+rl(yl.transform(o))+")"},wt={test:i=>ua.test(i)||Lr.test(i)||Wa.test(i),parse:i=>ua.test(i)?ua.parse(i):Wa.test(i)?Wa.parse(i):Lr.parse(i),transform:i=>typeof i=="string"?i:i.hasOwnProperty("red")?ua.transform(i):Wa.transform(i),getAnimatableNone:i=>{const l=wt.parse(i);return l.alpha=0,wt.transform(l)}},U1=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function N1(i){return isNaN(i)&&typeof i=="string"&&(i.match(gc)?.length||0)+(i.match(U1)?.length||0)>0}const Sp="number",Tp="color",B1="var",j1="var(",Xm="${}",w1=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function pl(i){const l=i.toString(),u=[],o={color:[],number:[],var:[]},c=[];let d=0;const p=l.replace(w1,g=>(wt.test(g)?(o.color.push(d),c.push(Tp),u.push(wt.parse(g))):g.startsWith(j1)?(o.var.push(d),c.push(B1),u.push(g)):(o.number.push(d),c.push(Sp),u.push(parseFloat(g))),++d,Xm)).split(Xm);return{values:u,split:p,indexes:o,types:c}}function bp(i){return pl(i).values}function Ap(i){const{split:l,types:u}=pl(i),o=l.length;return c=>{let d="";for(let h=0;h<o;h++)if(d+=l[h],c[h]!==void 0){const p=u[h];p===Sp?d+=rl(c[h]):p===Tp?d+=wt.transform(c[h]):d+=c[h]}return d}}const L1=i=>typeof i=="number"?0:wt.test(i)?wt.getAnimatableNone(i):i;function H1(i){const l=bp(i);return Ap(i)(l.map(L1))}const Hn={test:N1,parse:bp,createTransformer:Ap,getAnimatableNone:H1};function Mr(i,l,u){return u<0&&(u+=1),u>1&&(u-=1),u<1/6?i+(l-i)*6*u:u<1/2?l:u<2/3?i+(l-i)*(2/3-u)*6:i}function q1({hue:i,saturation:l,lightness:u,alpha:o}){i/=360,l/=100,u/=100;let c=0,d=0,h=0;if(!l)c=d=h=u;else{const p=u<.5?u*(1+l):u+l-u*l,g=2*u-p;c=Mr(g,p,i+1/3),d=Mr(g,p,i),h=Mr(g,p,i-1/3)}return{red:Math.round(c*255),green:Math.round(d*255),blue:Math.round(h*255),alpha:o}}function ks(i,l){return u=>u>0?l:i}const Ot=(i,l,u)=>i+(l-i)*u,Dr=(i,l,u)=>{const o=i*i,c=u*(l*l-o)+o;return c<0?0:Math.sqrt(c)},Y1=[Lr,ua,Wa],G1=i=>Y1.find(l=>l.test(i));function Zm(i){const l=G1(i);if(!l)return!1;let u=l.parse(i);return l===Wa&&(u=q1(u)),u}const Km=(i,l)=>{const u=Zm(i),o=Zm(l);if(!u||!o)return ks(i,l);const c={...u};return d=>(c.red=Dr(u.red,o.red,d),c.green=Dr(u.green,o.green,d),c.blue=Dr(u.blue,o.blue,d),c.alpha=Ot(u.alpha,o.alpha,d),ua.transform(c))},Hr=new Set(["none","hidden"]);function X1(i,l){return Hr.has(i)?u=>u<=0?i:l:u=>u>=1?l:i}function Z1(i,l){return u=>Ot(i,l,u)}function Sc(i){return typeof i=="number"?Z1:typeof i=="string"?pc(i)?ks:wt.test(i)?Km:k1:Array.isArray(i)?xp:typeof i=="object"?wt.test(i)?Km:K1:ks}function xp(i,l){const u=[...i],o=u.length,c=i.map((d,h)=>Sc(d)(d,l[h]));return d=>{for(let h=0;h<o;h++)u[h]=c[h](d);return u}}function K1(i,l){const u={...i,...l},o={};for(const c in u)i[c]!==void 0&&l[c]!==void 0&&(o[c]=Sc(i[c])(i[c],l[c]));return c=>{for(const d in o)u[d]=o[d](c);return u}}function Q1(i,l){const u=[],o={color:0,var:0,number:0};for(let c=0;c<l.values.length;c++){const d=l.types[c],h=i.indexes[d][o[d]],p=i.values[h]??0;u[c]=p,o[d]++}return u}const k1=(i,l)=>{const u=Hn.createTransformer(l),o=pl(i),c=pl(l);return o.indexes.var.length===c.indexes.var.length&&o.indexes.color.length===c.indexes.color.length&&o.indexes.number.length>=c.indexes.number.length?Hr.has(i)&&!c.values.length||Hr.has(l)&&!o.values.length?X1(i,l):Tl(xp(Q1(o,c),c.values),u):ks(i,l)};function Ep(i,l,u){return typeof i=="number"&&typeof l=="number"&&typeof u=="number"?Ot(i,l,u):Sc(i)(i,l)}const J1=i=>{const l=({timestamp:u})=>i(u);return{start:(u=!0)=>Dt.update(l,u),stop:()=>Ln(l),now:()=>Ft.isProcessing?Ft.timestamp:re.now()}},Mp=(i,l,u=10)=>{let o="";const c=Math.max(Math.round(l/u),2);for(let d=0;d<c;d++)o+=Math.round(i(d/(c-1))*1e4)/1e4+", ";return`linear(${o.substring(0,o.length-2)})`},Js=2e4;function Tc(i){let l=0;const u=50;let o=i.next(l);for(;!o.done&&l<Js;)l+=u,o=i.next(l);return l>=Js?1/0:l}function P1(i,l=100,u){const o=u({...i,keyframes:[0,l]}),c=Math.min(Tc(o),Js);return{type:"keyframes",ease:d=>o.next(c*d).value/l,duration:Ze(c)}}const F1=5;function Dp(i,l,u){const o=Math.max(l-F1,0);return lp(u-i(o),l-o)}const zt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Rr=.001;function W1({duration:i=zt.duration,bounce:l=zt.bounce,velocity:u=zt.velocity,mass:o=zt.mass}){let c,d,h=1-l;h=cn(zt.minDamping,zt.maxDamping,h),i=cn(zt.minDuration,zt.maxDuration,Ze(i)),h<1?(c=y=>{const v=y*h,A=v*i,x=v-u,U=qr(y,h),w=Math.exp(-A);return Rr-x/U*w},d=y=>{const A=y*h*i,x=A*u+u,U=Math.pow(h,2)*Math.pow(y,2)*i,w=Math.exp(-A),L=qr(Math.pow(y,2),h);return(-c(y)+Rr>0?-1:1)*((x-U)*w)/L}):(c=y=>{const v=Math.exp(-y*i),A=(y-u)*i+1;return-Rr+v*A},d=y=>{const v=Math.exp(-y*i),A=(u-y)*(i*i);return v*A});const p=5/i,g=I1(c,d,p);if(i=Xe(i),isNaN(g))return{stiffness:zt.stiffness,damping:zt.damping,duration:i};{const y=Math.pow(g,2)*o;return{stiffness:y,damping:h*2*Math.sqrt(o*y),duration:i}}}const $1=12;function I1(i,l,u){let o=u;for(let c=1;c<$1;c++)o=o-i(o)/l(o);return o}function qr(i,l){return i*Math.sqrt(1-l*l)}const tS=["duration","bounce"],eS=["stiffness","damping","mass"];function Qm(i,l){return l.some(u=>i[u]!==void 0)}function nS(i){let l={velocity:zt.velocity,stiffness:zt.stiffness,damping:zt.damping,mass:zt.mass,isResolvedFromDuration:!1,...i};if(!Qm(i,eS)&&Qm(i,tS))if(i.visualDuration){const u=i.visualDuration,o=2*Math.PI/(u*1.2),c=o*o,d=2*cn(.05,1,1-(i.bounce||0))*Math.sqrt(c);l={...l,mass:zt.mass,stiffness:c,damping:d}}else{const u=W1(i);l={...l,...u,mass:zt.mass},l.isResolvedFromDuration=!0}return l}function Ps(i=zt.visualDuration,l=zt.bounce){const u=typeof i!="object"?{visualDuration:i,keyframes:[0,1],bounce:l}:i;let{restSpeed:o,restDelta:c}=u;const d=u.keyframes[0],h=u.keyframes[u.keyframes.length-1],p={done:!1,value:d},{stiffness:g,damping:y,mass:v,duration:A,velocity:x,isResolvedFromDuration:U}=nS({...u,velocity:-Ze(u.velocity||0)}),w=x||0,L=y/(2*Math.sqrt(g*v)),Y=h-d,q=Ze(Math.sqrt(g/v)),X=Math.abs(Y)<5;o||(o=X?zt.restSpeed.granular:zt.restSpeed.default),c||(c=X?zt.restDelta.granular:zt.restDelta.default);let H;if(L<1){const B=qr(q,L);H=tt=>{const it=Math.exp(-L*q*tt);return h-it*((w+L*q*Y)/B*Math.sin(B*tt)+Y*Math.cos(B*tt))}}else if(L===1)H=B=>h-Math.exp(-q*B)*(Y+(w+q*Y)*B);else{const B=q*Math.sqrt(L*L-1);H=tt=>{const it=Math.exp(-L*q*tt),F=Math.min(B*tt,300);return h-it*((w+L*q*Y)*Math.sinh(F)+B*Y*Math.cosh(F))/B}}const Q={calculatedDuration:U&&A||null,next:B=>{const tt=H(B);if(U)p.done=B>=A;else{let it=B===0?w:0;L<1&&(it=B===0?Xe(w):Dp(H,B,tt));const F=Math.abs(it)<=o,xt=Math.abs(h-tt)<=c;p.done=F&&xt}return p.value=p.done?h:tt,p},toString:()=>{const B=Math.min(Tc(Q),Js),tt=Mp(it=>Q.next(B*it).value,B,30);return B+"ms "+tt},toTransition:()=>{}};return Q}Ps.applyToOptions=i=>{const l=P1(i,100,Ps);return i.ease=l.ease,i.duration=Xe(l.duration),i.type="keyframes",i};function Yr({keyframes:i,velocity:l=0,power:u=.8,timeConstant:o=325,bounceDamping:c=10,bounceStiffness:d=500,modifyTarget:h,min:p,max:g,restDelta:y=.5,restSpeed:v}){const A=i[0],x={done:!1,value:A},U=F=>p!==void 0&&F<p||g!==void 0&&F>g,w=F=>p===void 0?g:g===void 0||Math.abs(p-F)<Math.abs(g-F)?p:g;let L=u*l;const Y=A+L,q=h===void 0?Y:h(Y);q!==Y&&(L=q-A);const X=F=>-L*Math.exp(-F/o),H=F=>q+X(F),Q=F=>{const xt=X(F),Lt=H(F);x.done=Math.abs(xt)<=y,x.value=x.done?q:Lt};let B,tt;const it=F=>{U(x.value)&&(B=F,tt=Ps({keyframes:[x.value,w(x.value)],velocity:Dp(H,F,x.value),damping:c,stiffness:d,restDelta:y,restSpeed:v}))};return it(0),{calculatedDuration:null,next:F=>{let xt=!1;return!tt&&B===void 0&&(xt=!0,Q(F),it(F)),B!==void 0&&F>=B?tt.next(F-B):(!xt&&Q(F),x)}}}function aS(i,l,u){const o=[],c=u||fn.mix||Ep,d=i.length-1;for(let h=0;h<d;h++){let p=c(i[h],i[h+1]);if(l){const g=Array.isArray(l)?l[h]||ze:l;p=Tl(g,p)}o.push(p)}return o}function iS(i,l,{clamp:u=!0,ease:o,mixer:c}={}){const d=i.length;if(cc(d===l.length),d===1)return()=>l[0];if(d===2&&l[0]===l[1])return()=>l[1];const h=i[0]===i[1];i[0]>i[d-1]&&(i=[...i].reverse(),l=[...l].reverse());const p=aS(l,o,c),g=p.length,y=v=>{if(h&&v<i[0])return l[0];let A=0;if(g>1)for(;A<i.length-2&&!(v<i[A+1]);A++);const x=ml(i[A],i[A+1],v);return p[A](x)};return u?v=>y(cn(i[0],i[d-1],v)):y}function lS(i,l){const u=i[i.length-1];for(let o=1;o<=l;o++){const c=ml(0,l,o);i.push(Ot(u,1,c))}}function sS(i){const l=[0];return lS(l,i.length-1),l}function uS(i,l){return i.map(u=>u*l)}function oS(i,l){return i.map(()=>l||mp).splice(0,i.length-1)}function cl({duration:i=300,keyframes:l,times:u,ease:o="easeInOut"}){const c=S1(o)?o.map(Ym):Ym(o),d={done:!1,value:l[0]},h=uS(u&&u.length===l.length?u:sS(l),i),p=iS(h,l,{ease:Array.isArray(c)?c:oS(l,c)});return{calculatedDuration:i,next:g=>(d.value=p(g),d.done=g>=i,d)}}const rS=i=>i!==null;function bc(i,{repeat:l,repeatType:u="loop"},o,c=1){const d=i.filter(rS),p=c<0||l&&u!=="loop"&&l%2===1?0:d.length-1;return!p||o===void 0?d[p]:o}const cS={decay:Yr,inertia:Yr,tween:cl,keyframes:cl,spring:Ps};function Rp(i){typeof i.type=="string"&&(i.type=cS[i.type])}class Ac{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(l=>{this.resolve=l})}notifyFinished(){this.resolve()}then(l,u){return this.finished.then(l,u)}}const fS=i=>i/100;class xc extends Ac{constructor(l){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:u}=this.options;u&&u.updatedAt!==re.now()&&this.tick(re.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=l,this.initAnimation(),this.play(),l.autoplay===!1&&this.pause()}initAnimation(){const{options:l}=this;Rp(l);const{type:u=cl,repeat:o=0,repeatDelay:c=0,repeatType:d,velocity:h=0}=l;let{keyframes:p}=l;const g=u||cl;g!==cl&&typeof p[0]!="number"&&(this.mixKeyframes=Tl(fS,Ep(p[0],p[1])),p=[0,100]);const y=g({...l,keyframes:p});d==="mirror"&&(this.mirroredGenerator=g({...l,keyframes:[...p].reverse(),velocity:-h})),y.calculatedDuration===null&&(y.calculatedDuration=Tc(y));const{calculatedDuration:v}=y;this.calculatedDuration=v,this.resolvedDuration=v+c,this.totalDuration=this.resolvedDuration*(o+1)-c,this.generator=y}updateTime(l){const u=Math.round(l-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=u}tick(l,u=!1){const{generator:o,totalDuration:c,mixKeyframes:d,mirroredGenerator:h,resolvedDuration:p,calculatedDuration:g}=this;if(this.startTime===null)return o.next(0);const{delay:y=0,keyframes:v,repeat:A,repeatType:x,repeatDelay:U,type:w,onUpdate:L,finalKeyframe:Y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,l):this.speed<0&&(this.startTime=Math.min(l-c/this.speed,this.startTime)),u?this.currentTime=l:this.updateTime(l);const q=this.currentTime-y*(this.playbackSpeed>=0?1:-1),X=this.playbackSpeed>=0?q<0:q>c;this.currentTime=Math.max(q,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let H=this.currentTime,Q=o;if(A){const F=Math.min(this.currentTime,c)/p;let xt=Math.floor(F),Lt=F%1;!Lt&&F>=1&&(Lt=1),Lt===1&&xt--,xt=Math.min(xt,A+1),!!(xt%2)&&(x==="reverse"?(Lt=1-Lt,U&&(Lt-=U/p)):x==="mirror"&&(Q=h)),H=cn(0,1,Lt)*p}const B=X?{done:!1,value:v[0]}:Q.next(H);d&&(B.value=d(B.value));let{done:tt}=B;!X&&g!==null&&(tt=this.playbackSpeed>=0?this.currentTime>=c:this.currentTime<=0);const it=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&tt);return it&&w!==Yr&&(B.value=bc(v,this.options,Y,this.speed)),L&&L(B.value),it&&this.finish(),B}then(l,u){return this.finished.then(l,u)}get duration(){return Ze(this.calculatedDuration)}get time(){return Ze(this.currentTime)}set time(l){l=Xe(l),this.currentTime=l,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=l:this.driver&&(this.startTime=this.driver.now()-l/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(l){this.updateTime(re.now());const u=this.playbackSpeed!==l;this.playbackSpeed=l,u&&(this.time=Ze(this.currentTime))}play(){if(this.isStopped)return;const{driver:l=J1,startTime:u}=this.options;this.driver||(this.driver=l(c=>this.tick(c))),this.options.onPlay?.();const o=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=o):this.holdTime!==null?this.startTime=o-this.holdTime:this.startTime||(this.startTime=u??o),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(re.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(l){return this.startTime=0,this.tick(l,!0)}attachTimeline(l){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),l.observe(this)}}function hS(i){for(let l=1;l<i.length;l++)i[l]??(i[l]=i[l-1])}const oa=i=>i*180/Math.PI,Gr=i=>{const l=oa(Math.atan2(i[1],i[0]));return Xr(l)},dS={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:i=>(Math.abs(i[0])+Math.abs(i[3]))/2,rotate:Gr,rotateZ:Gr,skewX:i=>oa(Math.atan(i[1])),skewY:i=>oa(Math.atan(i[2])),skew:i=>(Math.abs(i[1])+Math.abs(i[2]))/2},Xr=i=>(i=i%360,i<0&&(i+=360),i),km=Gr,Jm=i=>Math.sqrt(i[0]*i[0]+i[1]*i[1]),Pm=i=>Math.sqrt(i[4]*i[4]+i[5]*i[5]),mS={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Jm,scaleY:Pm,scale:i=>(Jm(i)+Pm(i))/2,rotateX:i=>Xr(oa(Math.atan2(i[6],i[5]))),rotateY:i=>Xr(oa(Math.atan2(-i[2],i[0]))),rotateZ:km,rotate:km,skewX:i=>oa(Math.atan(i[4])),skewY:i=>oa(Math.atan(i[1])),skew:i=>(Math.abs(i[1])+Math.abs(i[4]))/2};function Zr(i){return i.includes("scale")?1:0}function Kr(i,l){if(!i||i==="none")return Zr(l);const u=i.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let o,c;if(u)o=mS,c=u;else{const p=i.match(/^matrix\(([-\d.e\s,]+)\)$/u);o=dS,c=p}if(!c)return Zr(l);const d=o[l],h=c[1].split(",").map(pS);return typeof d=="function"?d(h):h[d]}const yS=(i,l)=>{const{transform:u="none"}=getComputedStyle(i);return Kr(u,l)};function pS(i){return parseFloat(i.trim())}const si=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ui=new Set(si),Fm=i=>i===li||i===at,gS=new Set(["x","y","z"]),vS=si.filter(i=>!gS.has(i));function SS(i){const l=[];return vS.forEach(u=>{const o=i.getValue(u);o!==void 0&&(l.push([u,o.get()]),o.set(u.startsWith("scale")?1:0))}),l}const ra={width:({x:i},{paddingLeft:l="0",paddingRight:u="0"})=>i.max-i.min-parseFloat(l)-parseFloat(u),height:({y:i},{paddingTop:l="0",paddingBottom:u="0"})=>i.max-i.min-parseFloat(l)-parseFloat(u),top:(i,{top:l})=>parseFloat(l),left:(i,{left:l})=>parseFloat(l),bottom:({y:i},{top:l})=>parseFloat(l)+(i.max-i.min),right:({x:i},{left:l})=>parseFloat(l)+(i.max-i.min),x:(i,{transform:l})=>Kr(l,"x"),y:(i,{transform:l})=>Kr(l,"y")};ra.translateX=ra.x;ra.translateY=ra.y;const ca=new Set;let Qr=!1,kr=!1,Jr=!1;function Op(){if(kr){const i=Array.from(ca).filter(o=>o.needsMeasurement),l=new Set(i.map(o=>o.element)),u=new Map;l.forEach(o=>{const c=SS(o);c.length&&(u.set(o,c),o.render())}),i.forEach(o=>o.measureInitialState()),l.forEach(o=>{o.render();const c=u.get(o);c&&c.forEach(([d,h])=>{o.getValue(d)?.set(h)})}),i.forEach(o=>o.measureEndState()),i.forEach(o=>{o.suspendedScrollY!==void 0&&window.scrollTo(0,o.suspendedScrollY)})}kr=!1,Qr=!1,ca.forEach(i=>i.complete(Jr)),ca.clear()}function Cp(){ca.forEach(i=>{i.readKeyframes(),i.needsMeasurement&&(kr=!0)})}function TS(){Jr=!0,Cp(),Op(),Jr=!1}class Ec{constructor(l,u,o,c,d,h=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...l],this.onComplete=u,this.name=o,this.motionValue=c,this.element=d,this.isAsync=h}scheduleResolve(){this.state="scheduled",this.isAsync?(ca.add(this),Qr||(Qr=!0,Dt.read(Cp),Dt.resolveKeyframes(Op))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:l,name:u,element:o,motionValue:c}=this;if(l[0]===null){const d=c?.get(),h=l[l.length-1];if(d!==void 0)l[0]=d;else if(o&&u){const p=o.readValue(u,h);p!=null&&(l[0]=p)}l[0]===void 0&&(l[0]=h),c&&d===void 0&&c.set(l[0])}hS(l)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(l=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,l),ca.delete(this)}cancel(){this.state==="scheduled"&&(ca.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const bS=i=>i.startsWith("--");function AS(i,l,u){bS(l)?i.style.setProperty(l,u):i.style[l]=u}const xS=fc(()=>window.ScrollTimeline!==void 0),ES={};function MS(i,l){const u=fc(i);return()=>ES[l]??u()}const Vp=MS(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),ol=([i,l,u,o])=>`cubic-bezier(${i}, ${l}, ${u}, ${o})`,Wm={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ol([0,.65,.55,1]),circOut:ol([.55,0,1,.45]),backIn:ol([.31,.01,.66,-.59]),backOut:ol([.33,1.53,.69,.99])};function zp(i,l){if(i)return typeof i=="function"?Vp()?Mp(i,l):"ease-out":yp(i)?ol(i):Array.isArray(i)?i.map(u=>zp(u,l)||Wm.easeOut):Wm[i]}function DS(i,l,u,{delay:o=0,duration:c=300,repeat:d=0,repeatType:h="loop",ease:p="easeOut",times:g}={},y=void 0){const v={[l]:u};g&&(v.offset=g);const A=zp(p,c);Array.isArray(A)&&(v.easing=A);const x={delay:o,duration:c,easing:Array.isArray(A)?"linear":A,fill:"both",iterations:d+1,direction:h==="reverse"?"alternate":"normal"};return y&&(x.pseudoElement=y),i.animate(v,x)}function _p(i){return typeof i=="function"&&"applyToOptions"in i}function RS({type:i,...l}){return _p(i)&&Vp()?i.applyToOptions(l):(l.duration??(l.duration=300),l.ease??(l.ease="easeOut"),l)}class OS extends Ac{constructor(l){if(super(),this.finishedTime=null,this.isStopped=!1,!l)return;const{element:u,name:o,keyframes:c,pseudoElement:d,allowFlatten:h=!1,finalKeyframe:p,onComplete:g}=l;this.isPseudoElement=!!d,this.allowFlatten=h,this.options=l,cc(typeof l.type!="string");const y=RS(l);this.animation=DS(u,o,c,y,d),y.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const v=bc(c,this.options,p,this.speed);this.updateMotionValue?this.updateMotionValue(v):AS(u,o,v),this.animation.cancel()}g?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:l}=this;l==="idle"||l==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const l=this.animation.effect?.getComputedTiming?.().duration||0;return Ze(Number(l))}get time(){return Ze(Number(this.animation.currentTime)||0)}set time(l){this.finishedTime=null,this.animation.currentTime=Xe(l)}get speed(){return this.animation.playbackRate}set speed(l){l<0&&(this.finishedTime=null),this.animation.playbackRate=l}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(l){this.animation.startTime=l}attachTimeline({timeline:l,observe:u}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,l&&xS()?(this.animation.timeline=l,ze):u(this)}}const Up={anticipate:fp,backInOut:cp,circInOut:dp};function CS(i){return i in Up}function VS(i){typeof i.ease=="string"&&CS(i.ease)&&(i.ease=Up[i.ease])}const $m=10;class zS extends OS{constructor(l){VS(l),Rp(l),super(l),l.startTime&&(this.startTime=l.startTime),this.options=l}updateMotionValue(l){const{motionValue:u,onUpdate:o,onComplete:c,element:d,...h}=this.options;if(!u)return;if(l!==void 0){u.set(l);return}const p=new xc({...h,autoplay:!1}),g=Xe(this.finishedTime??this.time);u.setWithVelocity(p.sample(g-$m).value,p.sample(g).value,$m),p.stop()}}const Im=(i,l)=>l==="zIndex"?!1:!!(typeof i=="number"||Array.isArray(i)||typeof i=="string"&&(Hn.test(i)||i==="0")&&!i.startsWith("url("));function _S(i){const l=i[0];if(i.length===1)return!0;for(let u=0;u<i.length;u++)if(i[u]!==l)return!0}function US(i,l,u,o){const c=i[0];if(c===null)return!1;if(l==="display"||l==="visibility")return!0;const d=i[i.length-1],h=Im(c,l),p=Im(d,l);return!h||!p?!1:_S(i)||(u==="spring"||_p(u))&&o}function Pr(i){i.duration=0,i.type}const NS=new Set(["opacity","clipPath","filter","transform"]),BS=fc(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function jS(i){const{motionValue:l,name:u,repeatDelay:o,repeatType:c,damping:d,type:h}=i;if(!(l?.owner?.current instanceof HTMLElement))return!1;const{onUpdate:g,transformTemplate:y}=l.owner.getProps();return BS()&&u&&NS.has(u)&&(u!=="transform"||!y)&&!g&&!o&&c!=="mirror"&&d!==0&&h!=="inertia"}const wS=40;class LS extends Ac{constructor({autoplay:l=!0,delay:u=0,type:o="keyframes",repeat:c=0,repeatDelay:d=0,repeatType:h="loop",keyframes:p,name:g,motionValue:y,element:v,...A}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=re.now();const x={autoplay:l,delay:u,type:o,repeat:c,repeatDelay:d,repeatType:h,name:g,motionValue:y,element:v,...A},U=v?.KeyframeResolver||Ec;this.keyframeResolver=new U(p,(w,L,Y)=>this.onKeyframesResolved(w,L,x,!Y),g,y,v),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(l,u,o,c){this.keyframeResolver=void 0;const{name:d,type:h,velocity:p,delay:g,isHandoff:y,onUpdate:v}=o;this.resolvedAt=re.now(),US(l,d,h,p)||((fn.instantAnimations||!g)&&v?.(bc(l,o,u)),l[0]=l[l.length-1],Pr(o),o.repeat=0);const x={startTime:c?this.resolvedAt?this.resolvedAt-this.createdAt>wS?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:u,...o,keyframes:l},U=!y&&jS(x)?new zS({...x,element:x.motionValue.owner.current}):new xc(x);U.finished.then(()=>this.notifyFinished()).catch(ze),this.pendingTimeline&&(this.stopTimeline=U.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=U}get finished(){return this._animation?this.animation.finished:this._finished}then(l,u){return this.finished.finally(l).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),TS()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(l){this.animation.time=l}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(l){this.animation.speed=l}get startTime(){return this.animation.startTime}attachTimeline(l){return this._animation?this.stopTimeline=this.animation.attachTimeline(l):this.pendingTimeline=l,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const HS=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function qS(i){const l=HS.exec(i);if(!l)return[,];const[,u,o,c]=l;return[`--${u??o}`,c]}function Np(i,l,u=1){const[o,c]=qS(i);if(!o)return;const d=window.getComputedStyle(l).getPropertyValue(o);if(d){const h=d.trim();return np(h)?parseFloat(h):h}return pc(c)?Np(c,l,u+1):c}function Mc(i,l){return i?.[l]??i?.default??i}const Bp=new Set(["width","height","top","left","right","bottom",...si]),YS={test:i=>i==="auto",parse:i=>i},jp=i=>l=>l.test(i),wp=[li,at,Ke,wn,_1,z1,YS],ty=i=>wp.find(jp(i));function GS(i){return typeof i=="number"?i===0:i!==null?i==="none"||i==="0"||ip(i):!0}const XS=new Set(["brightness","contrast","saturate","opacity"]);function ZS(i){const[l,u]=i.slice(0,-1).split("(");if(l==="drop-shadow")return i;const[o]=u.match(gc)||[];if(!o)return i;const c=u.replace(o,"");let d=XS.has(l)?1:0;return o!==u&&(d*=100),l+"("+d+c+")"}const KS=/\b([a-z-]*)\(.*?\)/gu,Fr={...Hn,getAnimatableNone:i=>{const l=i.match(KS);return l?l.map(ZS).join(" "):i}},ey={...li,transform:Math.round},QS={rotate:wn,rotateX:wn,rotateY:wn,rotateZ:wn,scale:qs,scaleX:qs,scaleY:qs,scaleZ:qs,skew:wn,skewX:wn,skewY:wn,distance:at,translateX:at,translateY:at,translateZ:at,x:at,y:at,z:at,perspective:at,transformPerspective:at,opacity:yl,originX:Gm,originY:Gm,originZ:at},Dc={borderWidth:at,borderTopWidth:at,borderRightWidth:at,borderBottomWidth:at,borderLeftWidth:at,borderRadius:at,radius:at,borderTopLeftRadius:at,borderTopRightRadius:at,borderBottomRightRadius:at,borderBottomLeftRadius:at,width:at,maxWidth:at,height:at,maxHeight:at,top:at,right:at,bottom:at,left:at,padding:at,paddingTop:at,paddingRight:at,paddingBottom:at,paddingLeft:at,margin:at,marginTop:at,marginRight:at,marginBottom:at,marginLeft:at,backgroundPositionX:at,backgroundPositionY:at,...QS,zIndex:ey,fillOpacity:yl,strokeOpacity:yl,numOctaves:ey},kS={...Dc,color:wt,backgroundColor:wt,outlineColor:wt,fill:wt,stroke:wt,borderColor:wt,borderTopColor:wt,borderRightColor:wt,borderBottomColor:wt,borderLeftColor:wt,filter:Fr,WebkitFilter:Fr},Lp=i=>kS[i];function Hp(i,l){let u=Lp(i);return u!==Fr&&(u=Hn),u.getAnimatableNone?u.getAnimatableNone(l):void 0}const JS=new Set(["auto","none","0"]);function PS(i,l,u){let o=0,c;for(;o<i.length&&!c;){const d=i[o];typeof d=="string"&&!JS.has(d)&&pl(d).values.length&&(c=i[o]),o++}if(c&&u)for(const d of l)i[d]=Hp(u,c)}class FS extends Ec{constructor(l,u,o,c,d){super(l,u,o,c,d,!0)}readKeyframes(){const{unresolvedKeyframes:l,element:u,name:o}=this;if(!u||!u.current)return;super.readKeyframes();for(let g=0;g<l.length;g++){let y=l[g];if(typeof y=="string"&&(y=y.trim(),pc(y))){const v=Np(y,u.current);v!==void 0&&(l[g]=v),g===l.length-1&&(this.finalKeyframe=y)}}if(this.resolveNoneKeyframes(),!Bp.has(o)||l.length!==2)return;const[c,d]=l,h=ty(c),p=ty(d);if(h!==p)if(Fm(h)&&Fm(p))for(let g=0;g<l.length;g++){const y=l[g];typeof y=="string"&&(l[g]=parseFloat(y))}else ra[o]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:l,name:u}=this,o=[];for(let c=0;c<l.length;c++)(l[c]===null||GS(l[c]))&&o.push(c);o.length&&PS(l,o,u)}measureInitialState(){const{element:l,unresolvedKeyframes:u,name:o}=this;if(!l||!l.current)return;o==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ra[o](l.measureViewportBox(),window.getComputedStyle(l.current)),u[0]=this.measuredOrigin;const c=u[u.length-1];c!==void 0&&l.getValue(o,c).jump(c,!1)}measureEndState(){const{element:l,name:u,unresolvedKeyframes:o}=this;if(!l||!l.current)return;const c=l.getValue(u);c&&c.jump(this.measuredOrigin,!1);const d=o.length-1,h=o[d];o[d]=ra[u](l.measureViewportBox(),window.getComputedStyle(l.current)),h!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=h),this.removedTransforms?.length&&this.removedTransforms.forEach(([p,g])=>{l.getValue(p).set(g)}),this.resolveNoneKeyframes()}}function WS(i,l,u){if(i instanceof EventTarget)return[i];if(typeof i=="string"){let o=document;const c=u?.[i]??o.querySelectorAll(i);return c?Array.from(c):[]}return Array.from(i)}const qp=(i,l)=>l&&typeof i=="number"?l.transform(i):i;function $S(i){return ap(i)&&"offsetHeight"in i}const ny=30,IS=i=>!isNaN(parseFloat(i));class tT{constructor(l,u={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=o=>{const c=re.now();if(this.updatedAt!==c&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(o),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const d of this.dependents)d.dirty()},this.hasAnimated=!1,this.setCurrent(l),this.owner=u.owner}setCurrent(l){this.current=l,this.updatedAt=re.now(),this.canTrackVelocity===null&&l!==void 0&&(this.canTrackVelocity=IS(this.current))}setPrevFrameValue(l=this.current){this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt}onChange(l){return this.on("change",l)}on(l,u){this.events[l]||(this.events[l]=new hc);const o=this.events[l].add(u);return l==="change"?()=>{o(),Dt.read(()=>{this.events.change.getSize()||this.stop()})}:o}clearListeners(){for(const l in this.events)this.events[l].clear()}attach(l,u){this.passiveEffect=l,this.stopPassiveEffect=u}set(l){this.passiveEffect?this.passiveEffect(l,this.updateAndNotify):this.updateAndNotify(l)}setWithVelocity(l,u,o){this.set(u),this.prev=void 0,this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt-o}jump(l,u=!0){this.updateAndNotify(l),this.prev=l,this.prevUpdatedAt=this.prevFrameValue=void 0,u&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(l){this.dependents||(this.dependents=new Set),this.dependents.add(l)}removeDependent(l){this.dependents&&this.dependents.delete(l)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const l=re.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||l-this.updatedAt>ny)return 0;const u=Math.min(this.updatedAt-this.prevUpdatedAt,ny);return lp(parseFloat(this.current)-parseFloat(this.prevFrameValue),u)}start(l){return this.stop(),new Promise(u=>{this.hasAnimated=!0,this.animation=l(u),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ai(i,l){return new tT(i,l)}const{schedule:Rc}=pp(queueMicrotask,!1),Be={x:!1,y:!1};function Yp(){return Be.x||Be.y}function eT(i){return i==="x"||i==="y"?Be[i]?null:(Be[i]=!0,()=>{Be[i]=!1}):Be.x||Be.y?null:(Be.x=Be.y=!0,()=>{Be.x=Be.y=!1})}function Gp(i,l){const u=WS(i),o=new AbortController,c={passive:!0,...l,signal:o.signal};return[u,c,()=>o.abort()]}function ay(i){return!(i.pointerType==="touch"||Yp())}function nT(i,l,u={}){const[o,c,d]=Gp(i,u),h=p=>{if(!ay(p))return;const{target:g}=p,y=l(g,p);if(typeof y!="function"||!g)return;const v=A=>{ay(A)&&(y(A),g.removeEventListener("pointerleave",v))};g.addEventListener("pointerleave",v,c)};return o.forEach(p=>{p.addEventListener("pointerenter",h,c)}),d}const Xp=(i,l)=>l?i===l?!0:Xp(i,l.parentElement):!1,Oc=i=>i.pointerType==="mouse"?typeof i.button!="number"||i.button<=0:i.isPrimary!==!1,aT=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function iT(i){return aT.has(i.tagName)||i.tabIndex!==-1}const Zs=new WeakSet;function iy(i){return l=>{l.key==="Enter"&&i(l)}}function Or(i,l){i.dispatchEvent(new PointerEvent("pointer"+l,{isPrimary:!0,bubbles:!0}))}const lT=(i,l)=>{const u=i.currentTarget;if(!u)return;const o=iy(()=>{if(Zs.has(u))return;Or(u,"down");const c=iy(()=>{Or(u,"up")}),d=()=>Or(u,"cancel");u.addEventListener("keyup",c,l),u.addEventListener("blur",d,l)});u.addEventListener("keydown",o,l),u.addEventListener("blur",()=>u.removeEventListener("keydown",o),l)};function ly(i){return Oc(i)&&!Yp()}function sT(i,l,u={}){const[o,c,d]=Gp(i,u),h=p=>{const g=p.currentTarget;if(!ly(p))return;Zs.add(g);const y=l(g,p),v=(U,w)=>{window.removeEventListener("pointerup",A),window.removeEventListener("pointercancel",x),Zs.has(g)&&Zs.delete(g),ly(U)&&typeof y=="function"&&y(U,{success:w})},A=U=>{v(U,g===window||g===document||u.useGlobalTarget||Xp(g,U.target))},x=U=>{v(U,!1)};window.addEventListener("pointerup",A,c),window.addEventListener("pointercancel",x,c)};return o.forEach(p=>{(u.useGlobalTarget?window:p).addEventListener("pointerdown",h,c),$S(p)&&(p.addEventListener("focus",y=>lT(y,c)),!iT(p)&&!p.hasAttribute("tabindex")&&(p.tabIndex=0))}),d}function Zp(i){return ap(i)&&"ownerSVGElement"in i}function uT(i){return Zp(i)&&i.tagName==="svg"}const ee=i=>!!(i&&i.getVelocity),oT=[...wp,wt,Hn],rT=i=>oT.find(jp(i)),Kp=W.createContext({transformPagePoint:i=>i,isStatic:!1,reducedMotion:"never"});function cT(i=!0){const l=W.useContext(uc);if(l===null)return[!0,null];const{isPresent:u,onExitComplete:o,register:c}=l,d=W.useId();W.useEffect(()=>{if(i)return c(d)},[i]);const h=W.useCallback(()=>i&&o&&o(d),[d,o,i]);return!u&&o?[!1,h]:[!0]}const Qp=W.createContext({strict:!1}),sy={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ii={};for(const i in sy)ii[i]={isEnabled:l=>sy[i].some(u=>!!l[u])};function fT(i){for(const l in i)ii[l]={...ii[l],...i[l]}}const hT=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Fs(i){return i.startsWith("while")||i.startsWith("drag")&&i!=="draggable"||i.startsWith("layout")||i.startsWith("onTap")||i.startsWith("onPan")||i.startsWith("onLayout")||hT.has(i)}let kp=i=>!Fs(i);function dT(i){typeof i=="function"&&(kp=l=>l.startsWith("on")?!Fs(l):i(l))}try{dT(require("@emotion/is-prop-valid").default)}catch{}function mT(i,l,u){const o={};for(const c in i)c==="values"&&typeof i.values=="object"||(kp(c)||u===!0&&Fs(c)||!l&&!Fs(c)||i.draggable&&c.startsWith("onDrag"))&&(o[c]=i[c]);return o}const $s=W.createContext({});function Is(i){return i!==null&&typeof i=="object"&&typeof i.start=="function"}function gl(i){return typeof i=="string"||Array.isArray(i)}const Cc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Vc=["initial",...Cc];function tu(i){return Is(i.animate)||Vc.some(l=>gl(i[l]))}function Jp(i){return!!(tu(i)||i.variants)}function yT(i,l){if(tu(i)){const{initial:u,animate:o}=i;return{initial:u===!1||gl(u)?u:void 0,animate:gl(o)?o:void 0}}return i.inherit!==!1?l:{}}function pT(i){const{initial:l,animate:u}=yT(i,W.useContext($s));return W.useMemo(()=>({initial:l,animate:u}),[uy(l),uy(u)])}function uy(i){return Array.isArray(i)?i.join(" "):i}const vl={};function gT(i){for(const l in i)vl[l]=i[l],yc(l)&&(vl[l].isCSSVariable=!0)}function Pp(i,{layout:l,layoutId:u}){return ui.has(i)||i.startsWith("origin")||(l||u!==void 0)&&(!!vl[i]||i==="opacity")}const vT={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ST=si.length;function TT(i,l,u){let o="",c=!0;for(let d=0;d<ST;d++){const h=si[d],p=i[h];if(p===void 0)continue;let g=!0;if(typeof p=="number"?g=p===(h.startsWith("scale")?1:0):g=parseFloat(p)===0,!g||u){const y=qp(p,Dc[h]);if(!g){c=!1;const v=vT[h]||h;o+=`${v}(${y}) `}u&&(l[h]=y)}}return o=o.trim(),u?o=u(l,c?"":o):c&&(o="none"),o}function zc(i,l,u){const{style:o,vars:c,transformOrigin:d}=i;let h=!1,p=!1;for(const g in l){const y=l[g];if(ui.has(g)){h=!0;continue}else if(yc(g)){c[g]=y;continue}else{const v=qp(y,Dc[g]);g.startsWith("origin")?(p=!0,d[g]=v):o[g]=v}}if(l.transform||(h||u?o.transform=TT(l,i.transform,u):o.transform&&(o.transform="none")),p){const{originX:g="50%",originY:y="50%",originZ:v=0}=d;o.transformOrigin=`${g} ${y} ${v}`}}const _c=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Fp(i,l,u){for(const o in l)!ee(l[o])&&!Pp(o,u)&&(i[o]=l[o])}function bT({transformTemplate:i},l){return W.useMemo(()=>{const u=_c();return zc(u,l,i),Object.assign({},u.vars,u.style)},[l])}function AT(i,l){const u=i.style||{},o={};return Fp(o,u,i),Object.assign(o,bT(i,l)),o}function xT(i,l){const u={},o=AT(i,l);return i.drag&&i.dragListener!==!1&&(u.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=i.drag===!0?"none":`pan-${i.drag==="x"?"y":"x"}`),i.tabIndex===void 0&&(i.onTap||i.onTapStart||i.whileTap)&&(u.tabIndex=0),u.style=o,u}const ET={offset:"stroke-dashoffset",array:"stroke-dasharray"},MT={offset:"strokeDashoffset",array:"strokeDasharray"};function DT(i,l,u=1,o=0,c=!0){i.pathLength=1;const d=c?ET:MT;i[d.offset]=at.transform(-o);const h=at.transform(l),p=at.transform(u);i[d.array]=`${h} ${p}`}function Wp(i,{attrX:l,attrY:u,attrScale:o,pathLength:c,pathSpacing:d=1,pathOffset:h=0,...p},g,y,v){if(zc(i,p,y),g){i.style.viewBox&&(i.attrs.viewBox=i.style.viewBox);return}i.attrs=i.style,i.style={};const{attrs:A,style:x}=i;A.transform&&(x.transform=A.transform,delete A.transform),(x.transform||A.transformOrigin)&&(x.transformOrigin=A.transformOrigin??"50% 50%",delete A.transformOrigin),x.transform&&(x.transformBox=v?.transformBox??"fill-box",delete A.transformBox),l!==void 0&&(A.x=l),u!==void 0&&(A.y=u),o!==void 0&&(A.scale=o),c!==void 0&&DT(A,c,d,h,!1)}const $p=()=>({..._c(),attrs:{}}),Ip=i=>typeof i=="string"&&i.toLowerCase()==="svg";function RT(i,l,u,o){const c=W.useMemo(()=>{const d=$p();return Wp(d,l,Ip(o),i.transformTemplate,i.style),{...d.attrs,style:{...d.style}}},[l]);if(i.style){const d={};Fp(d,i.style,i),c.style={...d,...c.style}}return c}const OT=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Uc(i){return typeof i!="string"||i.includes("-")?!1:!!(OT.indexOf(i)>-1||/[A-Z]/u.test(i))}function CT(i,l,u,{latestValues:o},c,d=!1){const p=(Uc(i)?RT:xT)(l,o,c,i),g=mT(l,typeof i=="string",d),y=i!==W.Fragment?{...g,...p,ref:u}:{},{children:v}=l,A=W.useMemo(()=>ee(v)?v.get():v,[v]);return W.createElement(i,{...y,children:A})}function oy(i){const l=[{},{}];return i?.values.forEach((u,o)=>{l[0][o]=u.get(),l[1][o]=u.getVelocity()}),l}function Nc(i,l,u,o){if(typeof l=="function"){const[c,d]=oy(o);l=l(u!==void 0?u:i.custom,c,d)}if(typeof l=="string"&&(l=i.variants&&i.variants[l]),typeof l=="function"){const[c,d]=oy(o);l=l(u!==void 0?u:i.custom,c,d)}return l}function Ks(i){return ee(i)?i.get():i}function VT({scrapeMotionValuesFromProps:i,createRenderState:l},u,o,c){return{latestValues:zT(u,o,c,i),renderState:l()}}function zT(i,l,u,o){const c={},d=o(i,{});for(const x in d)c[x]=Ks(d[x]);let{initial:h,animate:p}=i;const g=tu(i),y=Jp(i);l&&y&&!g&&i.inherit!==!1&&(h===void 0&&(h=l.initial),p===void 0&&(p=l.animate));let v=u?u.initial===!1:!1;v=v||h===!1;const A=v?p:h;if(A&&typeof A!="boolean"&&!Is(A)){const x=Array.isArray(A)?A:[A];for(let U=0;U<x.length;U++){const w=Nc(i,x[U]);if(w){const{transitionEnd:L,transition:Y,...q}=w;for(const X in q){let H=q[X];if(Array.isArray(H)){const Q=v?H.length-1:0;H=H[Q]}H!==null&&(c[X]=H)}for(const X in L)c[X]=L[X]}}}return c}const tg=i=>(l,u)=>{const o=W.useContext($s),c=W.useContext(uc),d=()=>VT(i,l,o,c);return u?d():f1(d)};function Bc(i,l,u){const{style:o}=i,c={};for(const d in o)(ee(o[d])||l.style&&ee(l.style[d])||Pp(d,i)||u?.getValue(d)?.liveStyle!==void 0)&&(c[d]=o[d]);return c}const _T=tg({scrapeMotionValuesFromProps:Bc,createRenderState:_c});function eg(i,l,u){const o=Bc(i,l,u);for(const c in i)if(ee(i[c])||ee(l[c])){const d=si.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;o[d]=i[c]}return o}const UT=tg({scrapeMotionValuesFromProps:eg,createRenderState:$p}),NT=Symbol.for("motionComponentSymbol");function $a(i){return i&&typeof i=="object"&&Object.prototype.hasOwnProperty.call(i,"current")}function BT(i,l,u){return W.useCallback(o=>{o&&i.onMount&&i.onMount(o),l&&(o?l.mount(o):l.unmount()),u&&(typeof u=="function"?u(o):$a(u)&&(u.current=o))},[l])}const jc=i=>i.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),jT="framerAppearId",ng="data-"+jc(jT),ag=W.createContext({});function wT(i,l,u,o,c){const{visualElement:d}=W.useContext($s),h=W.useContext(Qp),p=W.useContext(uc),g=W.useContext(Kp).reducedMotion,y=W.useRef(null);o=o||h.renderer,!y.current&&o&&(y.current=o(i,{visualState:l,parent:d,props:u,presenceContext:p,blockInitialAnimation:p?p.initial===!1:!1,reducedMotionConfig:g}));const v=y.current,A=W.useContext(ag);v&&!v.projection&&c&&(v.type==="html"||v.type==="svg")&&LT(y.current,u,c,A);const x=W.useRef(!1);W.useInsertionEffect(()=>{v&&x.current&&v.update(u,p)});const U=u[ng],w=W.useRef(!!U&&!window.MotionHandoffIsComplete?.(U)&&window.MotionHasOptimisedAnimation?.(U));return h1(()=>{v&&(x.current=!0,window.MotionIsMounted=!0,v.updateFeatures(),v.scheduleRenderMicrotask(),w.current&&v.animationState&&v.animationState.animateChanges())}),W.useEffect(()=>{v&&(!w.current&&v.animationState&&v.animationState.animateChanges(),w.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(U)}),w.current=!1),v.enteringChildren=void 0)}),v}function LT(i,l,u,o){const{layoutId:c,layout:d,drag:h,dragConstraints:p,layoutScroll:g,layoutRoot:y,layoutCrossfade:v}=l;i.projection=new u(i.latestValues,l["data-framer-portal-id"]?void 0:ig(i.parent)),i.projection.setOptions({layoutId:c,layout:d,alwaysMeasureLayout:!!h||p&&$a(p),visualElement:i,animationType:typeof d=="string"?d:"both",initialPromotionConfig:o,crossfade:v,layoutScroll:g,layoutRoot:y})}function ig(i){if(i)return i.options.allowProjection!==!1?i.projection:ig(i.parent)}function Cr(i,{forwardMotionProps:l=!1}={},u,o){u&&fT(u);const c=Uc(i)?UT:_T;function d(p,g){let y;const v={...W.useContext(Kp),...p,layoutId:HT(p)},{isStatic:A}=v,x=pT(p),U=c(p,A);if(!A&&sc){qT();const w=YT(v);y=w.MeasureLayout,x.visualElement=wT(i,U,v,o,w.ProjectionNode)}return J.jsxs($s.Provider,{value:x,children:[y&&x.visualElement?J.jsx(y,{visualElement:x.visualElement,...v}):null,CT(i,p,BT(U,x.visualElement,g),U,A,l)]})}d.displayName=`motion.${typeof i=="string"?i:`create(${i.displayName??i.name??""})`}`;const h=W.forwardRef(d);return h[NT]=i,h}function HT({layoutId:i}){const l=W.useContext(ep).id;return l&&i!==void 0?l+"-"+i:i}function qT(i,l){W.useContext(Qp).strict}function YT(i){const{drag:l,layout:u}=ii;if(!l&&!u)return{};const o={...l,...u};return{MeasureLayout:l?.isEnabled(i)||u?.isEnabled(i)?o.MeasureLayout:void 0,ProjectionNode:o.ProjectionNode}}function GT(i,l){if(typeof Proxy>"u")return Cr;const u=new Map,o=(d,h)=>Cr(d,h,i,l),c=(d,h)=>o(d,h);return new Proxy(c,{get:(d,h)=>h==="create"?o:(u.has(h)||u.set(h,Cr(h,void 0,i,l)),u.get(h))})}function lg({top:i,left:l,right:u,bottom:o}){return{x:{min:l,max:u},y:{min:i,max:o}}}function XT({x:i,y:l}){return{top:l.min,right:i.max,bottom:l.max,left:i.min}}function ZT(i,l){if(!l)return i;const u=l({x:i.left,y:i.top}),o=l({x:i.right,y:i.bottom});return{top:u.y,left:u.x,bottom:o.y,right:o.x}}function Vr(i){return i===void 0||i===1}function Wr({scale:i,scaleX:l,scaleY:u}){return!Vr(i)||!Vr(l)||!Vr(u)}function sa(i){return Wr(i)||sg(i)||i.z||i.rotate||i.rotateX||i.rotateY||i.skewX||i.skewY}function sg(i){return ry(i.x)||ry(i.y)}function ry(i){return i&&i!=="0%"}function Ws(i,l,u){const o=i-u,c=l*o;return u+c}function cy(i,l,u,o,c){return c!==void 0&&(i=Ws(i,c,o)),Ws(i,u,o)+l}function $r(i,l=0,u=1,o,c){i.min=cy(i.min,l,u,o,c),i.max=cy(i.max,l,u,o,c)}function ug(i,{x:l,y:u}){$r(i.x,l.translate,l.scale,l.originPoint),$r(i.y,u.translate,u.scale,u.originPoint)}const fy=.999999999999,hy=1.0000000000001;function KT(i,l,u,o=!1){const c=u.length;if(!c)return;l.x=l.y=1;let d,h;for(let p=0;p<c;p++){d=u[p],h=d.projectionDelta;const{visualElement:g}=d.options;g&&g.props.style&&g.props.style.display==="contents"||(o&&d.options.layoutScroll&&d.scroll&&d!==d.root&&ti(i,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),h&&(l.x*=h.x.scale,l.y*=h.y.scale,ug(i,h)),o&&sa(d.latestValues)&&ti(i,d.latestValues))}l.x<hy&&l.x>fy&&(l.x=1),l.y<hy&&l.y>fy&&(l.y=1)}function Ia(i,l){i.min=i.min+l,i.max=i.max+l}function dy(i,l,u,o,c=.5){const d=Ot(i.min,i.max,c);$r(i,l,u,d,o)}function ti(i,l){dy(i.x,l.x,l.scaleX,l.scale,l.originX),dy(i.y,l.y,l.scaleY,l.scale,l.originY)}function og(i,l){return lg(ZT(i.getBoundingClientRect(),l))}function QT(i,l,u){const o=og(i,u),{scroll:c}=l;return c&&(Ia(o.x,c.offset.x),Ia(o.y,c.offset.y)),o}const my=()=>({translate:0,scale:1,origin:0,originPoint:0}),ei=()=>({x:my(),y:my()}),yy=()=>({min:0,max:0}),Nt=()=>({x:yy(),y:yy()}),Ir={current:null},rg={current:!1};function kT(){if(rg.current=!0,!!sc)if(window.matchMedia){const i=window.matchMedia("(prefers-reduced-motion)"),l=()=>Ir.current=i.matches;i.addEventListener("change",l),l()}else Ir.current=!1}const JT=new WeakMap;function PT(i,l,u){for(const o in l){const c=l[o],d=u[o];if(ee(c))i.addValue(o,c);else if(ee(d))i.addValue(o,ai(c,{owner:i}));else if(d!==c)if(i.hasValue(o)){const h=i.getValue(o);h.liveStyle===!0?h.jump(c):h.hasAnimated||h.set(c)}else{const h=i.getStaticValue(o);i.addValue(o,ai(h!==void 0?h:c,{owner:i}))}}for(const o in u)l[o]===void 0&&i.removeValue(o);return l}const py=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class FT{scrapeMotionValuesFromProps(l,u,o){return{}}constructor({parent:l,props:u,presenceContext:o,reducedMotionConfig:c,blockInitialAnimation:d,visualState:h},p={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ec,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const x=re.now();this.renderScheduledAt<x&&(this.renderScheduledAt=x,Dt.render(this.render,!1,!0))};const{latestValues:g,renderState:y}=h;this.latestValues=g,this.baseTarget={...g},this.initialValues=u.initial?{...g}:{},this.renderState=y,this.parent=l,this.props=u,this.presenceContext=o,this.depth=l?l.depth+1:0,this.reducedMotionConfig=c,this.options=p,this.blockInitialAnimation=!!d,this.isControllingVariants=tu(u),this.isVariantNode=Jp(u),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(l&&l.current);const{willChange:v,...A}=this.scrapeMotionValuesFromProps(u,{},this);for(const x in A){const U=A[x];g[x]!==void 0&&ee(U)&&U.set(g[x])}}mount(l){this.current=l,JT.set(l,this),this.projection&&!this.projection.instance&&this.projection.mount(l),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((u,o)=>this.bindToMotionValue(o,u)),rg.current||kT(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ir.current,this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Ln(this.notifyUpdate),Ln(this.render),this.valueSubscriptions.forEach(l=>l()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this);for(const l in this.events)this.events[l].clear();for(const l in this.features){const u=this.features[l];u&&(u.unmount(),u.isMounted=!1)}this.current=null}addChild(l){this.children.add(l),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(l)}removeChild(l){this.children.delete(l),this.enteringChildren&&this.enteringChildren.delete(l)}bindToMotionValue(l,u){this.valueSubscriptions.has(l)&&this.valueSubscriptions.get(l)();const o=ui.has(l);o&&this.onBindTransform&&this.onBindTransform();const c=u.on("change",h=>{this.latestValues[l]=h,this.props.onUpdate&&Dt.preRender(this.notifyUpdate),o&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let d;window.MotionCheckAppearSync&&(d=window.MotionCheckAppearSync(this,l,u)),this.valueSubscriptions.set(l,()=>{c(),d&&d(),u.owner&&u.stop()})}sortNodePosition(l){return!this.current||!this.sortInstanceNodePosition||this.type!==l.type?0:this.sortInstanceNodePosition(this.current,l.current)}updateFeatures(){let l="animation";for(l in ii){const u=ii[l];if(!u)continue;const{isEnabled:o,Feature:c}=u;if(!this.features[l]&&c&&o(this.props)&&(this.features[l]=new c(this)),this.features[l]){const d=this.features[l];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Nt()}getStaticValue(l){return this.latestValues[l]}setStaticValue(l,u){this.latestValues[l]=u}update(l,u){(l.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=l,this.prevPresenceContext=this.presenceContext,this.presenceContext=u;for(let o=0;o<py.length;o++){const c=py[o];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const d="on"+c,h=l[d];h&&(this.propEventSubscriptions[c]=this.on(c,h))}this.prevMotionValues=PT(this,this.scrapeMotionValuesFromProps(l,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(l){return this.props.variants?this.props.variants[l]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(l){const u=this.getClosestVariantNode();if(u)return u.variantChildren&&u.variantChildren.add(l),()=>u.variantChildren.delete(l)}addValue(l,u){const o=this.values.get(l);u!==o&&(o&&this.removeValue(l),this.bindToMotionValue(l,u),this.values.set(l,u),this.latestValues[l]=u.get())}removeValue(l){this.values.delete(l);const u=this.valueSubscriptions.get(l);u&&(u(),this.valueSubscriptions.delete(l)),delete this.latestValues[l],this.removeValueFromRenderState(l,this.renderState)}hasValue(l){return this.values.has(l)}getValue(l,u){if(this.props.values&&this.props.values[l])return this.props.values[l];let o=this.values.get(l);return o===void 0&&u!==void 0&&(o=ai(u===null?void 0:u,{owner:this}),this.addValue(l,o)),o}readValue(l,u){let o=this.latestValues[l]!==void 0||!this.current?this.latestValues[l]:this.getBaseTargetFromProps(this.props,l)??this.readValueFromInstance(this.current,l,this.options);return o!=null&&(typeof o=="string"&&(np(o)||ip(o))?o=parseFloat(o):!rT(o)&&Hn.test(u)&&(o=Hp(l,u)),this.setBaseTarget(l,ee(o)?o.get():o)),ee(o)?o.get():o}setBaseTarget(l,u){this.baseTarget[l]=u}getBaseTarget(l){const{initial:u}=this.props;let o;if(typeof u=="string"||typeof u=="object"){const d=Nc(this.props,u,this.presenceContext?.custom);d&&(o=d[l])}if(u&&o!==void 0)return o;const c=this.getBaseTargetFromProps(this.props,l);return c!==void 0&&!ee(c)?c:this.initialValues[l]!==void 0&&o===void 0?void 0:this.baseTarget[l]}on(l,u){return this.events[l]||(this.events[l]=new hc),this.events[l].add(u)}notify(l,...u){this.events[l]&&this.events[l].notify(...u)}scheduleRenderMicrotask(){Rc.render(this.render)}}class cg extends FT{constructor(){super(...arguments),this.KeyframeResolver=FS}sortInstanceNodePosition(l,u){return l.compareDocumentPosition(u)&2?1:-1}getBaseTargetFromProps(l,u){return l.style?l.style[u]:void 0}removeValueFromRenderState(l,{vars:u,style:o}){delete u[l],delete o[l]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:l}=this.props;ee(l)&&(this.childSubscription=l.on("change",u=>{this.current&&(this.current.textContent=`${u}`)}))}}function fg(i,{style:l,vars:u},o,c){const d=i.style;let h;for(h in l)d[h]=l[h];c?.applyProjectionStyles(d,o);for(h in u)d.setProperty(h,u[h])}function WT(i){return window.getComputedStyle(i)}class $T extends cg{constructor(){super(...arguments),this.type="html",this.renderInstance=fg}readValueFromInstance(l,u){if(ui.has(u))return this.projection?.isProjecting?Zr(u):yS(l,u);{const o=WT(l),c=(yc(u)?o.getPropertyValue(u):o[u])||0;return typeof c=="string"?c.trim():c}}measureInstanceViewportBox(l,{transformPagePoint:u}){return og(l,u)}build(l,u,o){zc(l,u,o.transformTemplate)}scrapeMotionValuesFromProps(l,u,o){return Bc(l,u,o)}}const hg=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function IT(i,l,u,o){fg(i,l,void 0,o);for(const c in l.attrs)i.setAttribute(hg.has(c)?c:jc(c),l.attrs[c])}class tb extends cg{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Nt}getBaseTargetFromProps(l,u){return l[u]}readValueFromInstance(l,u){if(ui.has(u)){const o=Lp(u);return o&&o.default||0}return u=hg.has(u)?u:jc(u),l.getAttribute(u)}scrapeMotionValuesFromProps(l,u,o){return eg(l,u,o)}build(l,u,o){Wp(l,u,this.isSVGTag,o.transformTemplate,o.style)}renderInstance(l,u,o,c){IT(l,u,o,c)}mount(l){this.isSVGTag=Ip(l.tagName),super.mount(l)}}const eb=(i,l)=>Uc(i)?new tb(l):new $T(l,{allowProjection:i!==W.Fragment});function ni(i,l,u){const o=i.getProps();return Nc(o,l,u!==void 0?u:o.custom,i)}const tc=i=>Array.isArray(i);function nb(i,l,u){i.hasValue(l)?i.getValue(l).set(u):i.addValue(l,ai(u))}function ab(i){return tc(i)?i[i.length-1]||0:i}function ib(i,l){const u=ni(i,l);let{transitionEnd:o={},transition:c={},...d}=u||{};d={...d,...o};for(const h in d){const p=ab(d[h]);nb(i,h,p)}}function lb(i){return!!(ee(i)&&i.add)}function ec(i,l){const u=i.getValue("willChange");if(lb(u))return u.add(l);if(!u&&fn.WillChange){const o=new fn.WillChange("auto");i.addValue("willChange",o),o.add(l)}}function dg(i){return i.props[ng]}const sb=i=>i!==null;function ub(i,{repeat:l,repeatType:u="loop"},o){const c=i.filter(sb),d=l&&u!=="loop"&&l%2===1?0:c.length-1;return c[d]}const ob={type:"spring",stiffness:500,damping:25,restSpeed:10},rb=i=>({type:"spring",stiffness:550,damping:i===0?2*Math.sqrt(550):30,restSpeed:10}),cb={type:"keyframes",duration:.8},fb={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},hb=(i,{keyframes:l})=>l.length>2?cb:ui.has(i)?i.startsWith("scale")?rb(l[1]):ob:fb;function db({when:i,delay:l,delayChildren:u,staggerChildren:o,staggerDirection:c,repeat:d,repeatType:h,repeatDelay:p,from:g,elapsed:y,...v}){return!!Object.keys(v).length}const wc=(i,l,u,o={},c,d)=>h=>{const p=Mc(o,i)||{},g=p.delay||o.delay||0;let{elapsed:y=0}=o;y=y-Xe(g);const v={keyframes:Array.isArray(u)?u:[null,u],ease:"easeOut",velocity:l.getVelocity(),...p,delay:-y,onUpdate:x=>{l.set(x),p.onUpdate&&p.onUpdate(x)},onComplete:()=>{h(),p.onComplete&&p.onComplete()},name:i,motionValue:l,element:d?void 0:c};db(p)||Object.assign(v,hb(i,v)),v.duration&&(v.duration=Xe(v.duration)),v.repeatDelay&&(v.repeatDelay=Xe(v.repeatDelay)),v.from!==void 0&&(v.keyframes[0]=v.from);let A=!1;if((v.type===!1||v.duration===0&&!v.repeatDelay)&&(Pr(v),v.delay===0&&(A=!0)),(fn.instantAnimations||fn.skipAnimations)&&(A=!0,Pr(v),v.delay=0),v.allowFlatten=!p.type&&!p.ease,A&&!d&&l.get()!==void 0){const x=ub(v.keyframes,p);if(x!==void 0){Dt.update(()=>{v.onUpdate(x),v.onComplete()});return}}return p.isSync?new xc(v):new LS(v)};function mb({protectedKeys:i,needsAnimating:l},u){const o=i.hasOwnProperty(u)&&l[u]!==!0;return l[u]=!1,o}function mg(i,l,{delay:u=0,transitionOverride:o,type:c}={}){let{transition:d=i.getDefaultTransition(),transitionEnd:h,...p}=l;o&&(d=o);const g=[],y=c&&i.animationState&&i.animationState.getState()[c];for(const v in p){const A=i.getValue(v,i.latestValues[v]??null),x=p[v];if(x===void 0||y&&mb(y,v))continue;const U={delay:u,...Mc(d||{},v)},w=A.get();if(w!==void 0&&!A.isAnimating&&!Array.isArray(x)&&x===w&&!U.velocity)continue;let L=!1;if(window.MotionHandoffAnimation){const q=dg(i);if(q){const X=window.MotionHandoffAnimation(q,v,Dt);X!==null&&(U.startTime=X,L=!0)}}ec(i,v),A.start(wc(v,A,x,i.shouldReduceMotion&&Bp.has(v)?{type:!1}:U,i,L));const Y=A.animation;Y&&g.push(Y)}return h&&Promise.all(g).then(()=>{Dt.update(()=>{h&&ib(i,h)})}),g}function yg(i,l,u,o=0,c=1){const d=Array.from(i).sort((y,v)=>y.sortNodePosition(v)).indexOf(l),h=i.size,p=(h-1)*o;return typeof u=="function"?u(d,h):c===1?d*o:p-d*o}function nc(i,l,u={}){const o=ni(i,l,u.type==="exit"?i.presenceContext?.custom:void 0);let{transition:c=i.getDefaultTransition()||{}}=o||{};u.transitionOverride&&(c=u.transitionOverride);const d=o?()=>Promise.all(mg(i,o,u)):()=>Promise.resolve(),h=i.variantChildren&&i.variantChildren.size?(g=0)=>{const{delayChildren:y=0,staggerChildren:v,staggerDirection:A}=c;return yb(i,l,g,y,v,A,u)}:()=>Promise.resolve(),{when:p}=c;if(p){const[g,y]=p==="beforeChildren"?[d,h]:[h,d];return g().then(()=>y())}else return Promise.all([d(),h(u.delay)])}function yb(i,l,u=0,o=0,c=0,d=1,h){const p=[];for(const g of i.variantChildren)g.notify("AnimationStart",l),p.push(nc(g,l,{...h,delay:u+(typeof o=="function"?0:o)+yg(i.variantChildren,g,o,c,d)}).then(()=>g.notify("AnimationComplete",l)));return Promise.all(p)}function pb(i,l,u={}){i.notify("AnimationStart",l);let o;if(Array.isArray(l)){const c=l.map(d=>nc(i,d,u));o=Promise.all(c)}else if(typeof l=="string")o=nc(i,l,u);else{const c=typeof l=="function"?ni(i,l,u.custom):l;o=Promise.all(mg(i,c,u))}return o.then(()=>{i.notify("AnimationComplete",l)})}function pg(i,l){if(!Array.isArray(l))return!1;const u=l.length;if(u!==i.length)return!1;for(let o=0;o<u;o++)if(l[o]!==i[o])return!1;return!0}const gb=Vc.length;function gg(i){if(!i)return;if(!i.isControllingVariants){const u=i.parent?gg(i.parent)||{}:{};return i.props.initial!==void 0&&(u.initial=i.props.initial),u}const l={};for(let u=0;u<gb;u++){const o=Vc[u],c=i.props[o];(gl(c)||c===!1)&&(l[o]=c)}return l}const vb=[...Cc].reverse(),Sb=Cc.length;function Tb(i){return l=>Promise.all(l.map(({animation:u,options:o})=>pb(i,u,o)))}function bb(i){let l=Tb(i),u=gy(),o=!0;const c=g=>(y,v)=>{const A=ni(i,v,g==="exit"?i.presenceContext?.custom:void 0);if(A){const{transition:x,transitionEnd:U,...w}=A;y={...y,...w,...U}}return y};function d(g){l=g(i)}function h(g){const{props:y}=i,v=gg(i.parent)||{},A=[],x=new Set;let U={},w=1/0;for(let Y=0;Y<Sb;Y++){const q=vb[Y],X=u[q],H=y[q]!==void 0?y[q]:v[q],Q=gl(H),B=q===g?X.isActive:null;B===!1&&(w=Y);let tt=H===v[q]&&H!==y[q]&&Q;if(tt&&o&&i.manuallyAnimateOnMount&&(tt=!1),X.protectedKeys={...U},!X.isActive&&B===null||!H&&!X.prevProp||Is(H)||typeof H=="boolean")continue;const it=Ab(X.prevProp,H);let F=it||q===g&&X.isActive&&!tt&&Q||Y>w&&Q,xt=!1;const Lt=Array.isArray(H)?H:[H];let Wt=Lt.reduce(c(q),{});B===!1&&(Wt={});const{prevResolvedValues:Ht={}}=X,Qe={...Ht,...Wt},je=j=>{F=!0,x.has(j)&&(xt=!0,x.delete(j)),X.needsAnimating[j]=!0;const k=i.getValue(j);k&&(k.liveStyle=!1)};for(const j in Qe){const k=Wt[j],ft=Ht[j];if(U.hasOwnProperty(j))continue;let T=!1;tc(k)&&tc(ft)?T=!pg(k,ft):T=k!==ft,T?k!=null?je(j):x.add(j):k!==void 0&&x.has(j)?je(j):X.protectedKeys[j]=!0}X.prevProp=H,X.prevResolvedValues=Wt,X.isActive&&(U={...U,...Wt}),o&&i.blockInitialAnimation&&(F=!1);const qt=tt&&it;F&&(!qt||xt)&&A.push(...Lt.map(j=>{const k={type:q};if(typeof j=="string"&&o&&!qt&&i.manuallyAnimateOnMount&&i.parent){const{parent:ft}=i,T=ni(ft,j);if(ft.enteringChildren&&T){const{delayChildren:N}=T.transition||{};k.delay=yg(ft.enteringChildren,i,N)}}return{animation:j,options:k}}))}if(x.size){const Y={};if(typeof y.initial!="boolean"){const q=ni(i,Array.isArray(y.initial)?y.initial[0]:y.initial);q&&q.transition&&(Y.transition=q.transition)}x.forEach(q=>{const X=i.getBaseTarget(q),H=i.getValue(q);H&&(H.liveStyle=!0),Y[q]=X??null}),A.push({animation:Y})}let L=!!A.length;return o&&(y.initial===!1||y.initial===y.animate)&&!i.manuallyAnimateOnMount&&(L=!1),o=!1,L?l(A):Promise.resolve()}function p(g,y){if(u[g].isActive===y)return Promise.resolve();i.variantChildren?.forEach(A=>A.animationState?.setActive(g,y)),u[g].isActive=y;const v=h(g);for(const A in u)u[A].protectedKeys={};return v}return{animateChanges:h,setActive:p,setAnimateFunction:d,getState:()=>u,reset:()=>{u=gy(),o=!0}}}function Ab(i,l){return typeof l=="string"?l!==i:Array.isArray(l)?!pg(l,i):!1}function la(i=!1){return{isActive:i,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function gy(){return{animate:la(!0),whileInView:la(),whileHover:la(),whileTap:la(),whileDrag:la(),whileFocus:la(),exit:la()}}class qn{constructor(l){this.isMounted=!1,this.node=l}update(){}}class xb extends qn{constructor(l){super(l),l.animationState||(l.animationState=bb(l))}updateAnimationControlsSubscription(){const{animate:l}=this.node.getProps();Is(l)&&(this.unmountControls=l.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:l}=this.node.getProps(),{animate:u}=this.node.prevProps||{};l!==u&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let Eb=0;class Mb extends qn{constructor(){super(...arguments),this.id=Eb++}update(){if(!this.node.presenceContext)return;const{isPresent:l,onExitComplete:u}=this.node.presenceContext,{isPresent:o}=this.node.prevPresenceContext||{};if(!this.node.animationState||l===o)return;const c=this.node.animationState.setActive("exit",!l);u&&!l&&c.then(()=>{u(this.id)})}mount(){const{register:l,onExitComplete:u}=this.node.presenceContext||{};u&&u(this.id),l&&(this.unmount=l(this.id))}unmount(){}}const Db={animation:{Feature:xb},exit:{Feature:Mb}};function Sl(i,l,u,o={passive:!0}){return i.addEventListener(l,u,o),()=>i.removeEventListener(l,u)}function xl(i){return{point:{x:i.pageX,y:i.pageY}}}const Rb=i=>l=>Oc(l)&&i(l,xl(l));function fl(i,l,u,o){return Sl(i,l,Rb(u),o)}const vg=1e-4,Ob=1-vg,Cb=1+vg,Sg=.01,Vb=0-Sg,zb=0+Sg;function ae(i){return i.max-i.min}function _b(i,l,u){return Math.abs(i-l)<=u}function vy(i,l,u,o=.5){i.origin=o,i.originPoint=Ot(l.min,l.max,i.origin),i.scale=ae(u)/ae(l),i.translate=Ot(u.min,u.max,i.origin)-i.originPoint,(i.scale>=Ob&&i.scale<=Cb||isNaN(i.scale))&&(i.scale=1),(i.translate>=Vb&&i.translate<=zb||isNaN(i.translate))&&(i.translate=0)}function hl(i,l,u,o){vy(i.x,l.x,u.x,o?o.originX:void 0),vy(i.y,l.y,u.y,o?o.originY:void 0)}function Sy(i,l,u){i.min=u.min+l.min,i.max=i.min+ae(l)}function Ub(i,l,u){Sy(i.x,l.x,u.x),Sy(i.y,l.y,u.y)}function Ty(i,l,u){i.min=l.min-u.min,i.max=i.min+ae(l)}function dl(i,l,u){Ty(i.x,l.x,u.x),Ty(i.y,l.y,u.y)}function Ve(i){return[i("x"),i("y")]}const Tg=({current:i})=>i?i.ownerDocument.defaultView:null,by=(i,l)=>Math.abs(i-l);function Nb(i,l){const u=by(i.x,l.x),o=by(i.y,l.y);return Math.sqrt(u**2+o**2)}class bg{constructor(l,u,{transformPagePoint:o,contextWindow:c=window,dragSnapToOrigin:d=!1,distanceThreshold:h=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=_r(this.lastMoveEventInfo,this.history),U=this.startEvent!==null,w=Nb(x.offset,{x:0,y:0})>=this.distanceThreshold;if(!U&&!w)return;const{point:L}=x,{timestamp:Y}=Ft;this.history.push({...L,timestamp:Y});const{onStart:q,onMove:X}=this.handlers;U||(q&&q(this.lastMoveEvent,x),this.startEvent=this.lastMoveEvent),X&&X(this.lastMoveEvent,x)},this.handlePointerMove=(x,U)=>{this.lastMoveEvent=x,this.lastMoveEventInfo=zr(U,this.transformPagePoint),Dt.update(this.updatePoint,!0)},this.handlePointerUp=(x,U)=>{this.end();const{onEnd:w,onSessionEnd:L,resumeAnimation:Y}=this.handlers;if(this.dragSnapToOrigin&&Y&&Y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const q=_r(x.type==="pointercancel"?this.lastMoveEventInfo:zr(U,this.transformPagePoint),this.history);this.startEvent&&w&&w(x,q),L&&L(x,q)},!Oc(l))return;this.dragSnapToOrigin=d,this.handlers=u,this.transformPagePoint=o,this.distanceThreshold=h,this.contextWindow=c||window;const p=xl(l),g=zr(p,this.transformPagePoint),{point:y}=g,{timestamp:v}=Ft;this.history=[{...y,timestamp:v}];const{onSessionStart:A}=u;A&&A(l,_r(g,this.history)),this.removeListeners=Tl(fl(this.contextWindow,"pointermove",this.handlePointerMove),fl(this.contextWindow,"pointerup",this.handlePointerUp),fl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(l){this.handlers=l}end(){this.removeListeners&&this.removeListeners(),Ln(this.updatePoint)}}function zr(i,l){return l?{point:l(i.point)}:i}function Ay(i,l){return{x:i.x-l.x,y:i.y-l.y}}function _r({point:i},l){return{point:i,delta:Ay(i,Ag(l)),offset:Ay(i,Bb(l)),velocity:jb(l,.1)}}function Bb(i){return i[0]}function Ag(i){return i[i.length-1]}function jb(i,l){if(i.length<2)return{x:0,y:0};let u=i.length-1,o=null;const c=Ag(i);for(;u>=0&&(o=i[u],!(c.timestamp-o.timestamp>Xe(l)));)u--;if(!o)return{x:0,y:0};const d=Ze(c.timestamp-o.timestamp);if(d===0)return{x:0,y:0};const h={x:(c.x-o.x)/d,y:(c.y-o.y)/d};return h.x===1/0&&(h.x=0),h.y===1/0&&(h.y=0),h}function wb(i,{min:l,max:u},o){return l!==void 0&&i<l?i=o?Ot(l,i,o.min):Math.max(i,l):u!==void 0&&i>u&&(i=o?Ot(u,i,o.max):Math.min(i,u)),i}function xy(i,l,u){return{min:l!==void 0?i.min+l:void 0,max:u!==void 0?i.max+u-(i.max-i.min):void 0}}function Lb(i,{top:l,left:u,bottom:o,right:c}){return{x:xy(i.x,u,c),y:xy(i.y,l,o)}}function Ey(i,l){let u=l.min-i.min,o=l.max-i.max;return l.max-l.min<i.max-i.min&&([u,o]=[o,u]),{min:u,max:o}}function Hb(i,l){return{x:Ey(i.x,l.x),y:Ey(i.y,l.y)}}function qb(i,l){let u=.5;const o=ae(i),c=ae(l);return c>o?u=ml(l.min,l.max-o,i.min):o>c&&(u=ml(i.min,i.max-c,l.min)),cn(0,1,u)}function Yb(i,l){const u={};return l.min!==void 0&&(u.min=l.min-i.min),l.max!==void 0&&(u.max=l.max-i.min),u}const ac=.35;function Gb(i=ac){return i===!1?i=0:i===!0&&(i=ac),{x:My(i,"left","right"),y:My(i,"top","bottom")}}function My(i,l,u){return{min:Dy(i,l),max:Dy(i,u)}}function Dy(i,l){return typeof i=="number"?i:i[l]||0}const Xb=new WeakMap;class Zb{constructor(l){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Nt(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=l}start(l,{snapToCursor:u=!1,distanceThreshold:o}={}){const{presenceContext:c}=this.visualElement;if(c&&c.isPresent===!1)return;const d=A=>{const{dragSnapToOrigin:x}=this.getProps();x?this.pauseAnimation():this.stopAnimation(),u&&this.snapToCursor(xl(A).point)},h=(A,x)=>{const{drag:U,dragPropagation:w,onDragStart:L}=this.getProps();if(U&&!w&&(this.openDragLock&&this.openDragLock(),this.openDragLock=eT(U),!this.openDragLock))return;this.latestPointerEvent=A,this.latestPanInfo=x,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ve(q=>{let X=this.getAxisMotionValue(q).get()||0;if(Ke.test(X)){const{projection:H}=this.visualElement;if(H&&H.layout){const Q=H.layout.layoutBox[q];Q&&(X=ae(Q)*(parseFloat(X)/100))}}this.originPoint[q]=X}),L&&Dt.postRender(()=>L(A,x)),ec(this.visualElement,"transform");const{animationState:Y}=this.visualElement;Y&&Y.setActive("whileDrag",!0)},p=(A,x)=>{this.latestPointerEvent=A,this.latestPanInfo=x;const{dragPropagation:U,dragDirectionLock:w,onDirectionLock:L,onDrag:Y}=this.getProps();if(!U&&!this.openDragLock)return;const{offset:q}=x;if(w&&this.currentDirection===null){this.currentDirection=Kb(q),this.currentDirection!==null&&L&&L(this.currentDirection);return}this.updateAxis("x",x.point,q),this.updateAxis("y",x.point,q),this.visualElement.render(),Y&&Y(A,x)},g=(A,x)=>{this.latestPointerEvent=A,this.latestPanInfo=x,this.stop(A,x),this.latestPointerEvent=null,this.latestPanInfo=null},y=()=>Ve(A=>this.getAnimationState(A)==="paused"&&this.getAxisMotionValue(A).animation?.play()),{dragSnapToOrigin:v}=this.getProps();this.panSession=new bg(l,{onSessionStart:d,onStart:h,onMove:p,onSessionEnd:g,resumeAnimation:y},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:v,distanceThreshold:o,contextWindow:Tg(this.visualElement)})}stop(l,u){const o=l||this.latestPointerEvent,c=u||this.latestPanInfo,d=this.isDragging;if(this.cancel(),!d||!c||!o)return;const{velocity:h}=c;this.startAnimation(h);const{onDragEnd:p}=this.getProps();p&&Dt.postRender(()=>p(o,c))}cancel(){this.isDragging=!1;const{projection:l,animationState:u}=this.visualElement;l&&(l.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:o}=this.getProps();!o&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),u&&u.setActive("whileDrag",!1)}updateAxis(l,u,o){const{drag:c}=this.getProps();if(!o||!Ys(l,c,this.currentDirection))return;const d=this.getAxisMotionValue(l);let h=this.originPoint[l]+o[l];this.constraints&&this.constraints[l]&&(h=wb(h,this.constraints[l],this.elastic[l])),d.set(h)}resolveConstraints(){const{dragConstraints:l,dragElastic:u}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,c=this.constraints;l&&$a(l)?this.constraints||(this.constraints=this.resolveRefConstraints()):l&&o?this.constraints=Lb(o.layoutBox,l):this.constraints=!1,this.elastic=Gb(u),c!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&Ve(d=>{this.constraints!==!1&&this.getAxisMotionValue(d)&&(this.constraints[d]=Yb(o.layoutBox[d],this.constraints[d]))})}resolveRefConstraints(){const{dragConstraints:l,onMeasureDragConstraints:u}=this.getProps();if(!l||!$a(l))return!1;const o=l.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const d=QT(o,c.root,this.visualElement.getTransformPagePoint());let h=Hb(c.layout.layoutBox,d);if(u){const p=u(XT(h));this.hasMutatedConstraints=!!p,p&&(h=lg(p))}return h}startAnimation(l){const{drag:u,dragMomentum:o,dragElastic:c,dragTransition:d,dragSnapToOrigin:h,onDragTransitionEnd:p}=this.getProps(),g=this.constraints||{},y=Ve(v=>{if(!Ys(v,u,this.currentDirection))return;let A=g&&g[v]||{};h&&(A={min:0,max:0});const x=c?200:1e6,U=c?40:1e7,w={type:"inertia",velocity:o?l[v]:0,bounceStiffness:x,bounceDamping:U,timeConstant:750,restDelta:1,restSpeed:10,...d,...A};return this.startAxisValueAnimation(v,w)});return Promise.all(y).then(p)}startAxisValueAnimation(l,u){const o=this.getAxisMotionValue(l);return ec(this.visualElement,l),o.start(wc(l,o,0,u,this.visualElement,!1))}stopAnimation(){Ve(l=>this.getAxisMotionValue(l).stop())}pauseAnimation(){Ve(l=>this.getAxisMotionValue(l).animation?.pause())}getAnimationState(l){return this.getAxisMotionValue(l).animation?.state}getAxisMotionValue(l){const u=`_drag${l.toUpperCase()}`,o=this.visualElement.getProps(),c=o[u];return c||this.visualElement.getValue(l,(o.initial?o.initial[l]:void 0)||0)}snapToCursor(l){Ve(u=>{const{drag:o}=this.getProps();if(!Ys(u,o,this.currentDirection))return;const{projection:c}=this.visualElement,d=this.getAxisMotionValue(u);if(c&&c.layout){const{min:h,max:p}=c.layout.layoutBox[u];d.set(l[u]-Ot(h,p,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:l,dragConstraints:u}=this.getProps(),{projection:o}=this.visualElement;if(!$a(u)||!o||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};Ve(h=>{const p=this.getAxisMotionValue(h);if(p&&this.constraints!==!1){const g=p.get();c[h]=qb({min:g,max:g},this.constraints[h])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",o.root&&o.root.updateScroll(),o.updateLayout(),this.resolveConstraints(),Ve(h=>{if(!Ys(h,l,null))return;const p=this.getAxisMotionValue(h),{min:g,max:y}=this.constraints[h];p.set(Ot(g,y,c[h]))})}addListeners(){if(!this.visualElement.current)return;Xb.set(this.visualElement,this);const l=this.visualElement.current,u=fl(l,"pointerdown",g=>{const{drag:y,dragListener:v=!0}=this.getProps();y&&v&&this.start(g)}),o=()=>{const{dragConstraints:g}=this.getProps();$a(g)&&g.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",o);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),Dt.read(o);const h=Sl(window,"resize",()=>this.scalePositionWithinConstraints()),p=c.addEventListener("didUpdate",(({delta:g,hasLayoutChanged:y})=>{this.isDragging&&y&&(Ve(v=>{const A=this.getAxisMotionValue(v);A&&(this.originPoint[v]+=g[v].translate,A.set(A.get()+g[v].translate))}),this.visualElement.render())}));return()=>{h(),u(),d(),p&&p()}}getProps(){const l=this.visualElement.getProps(),{drag:u=!1,dragDirectionLock:o=!1,dragPropagation:c=!1,dragConstraints:d=!1,dragElastic:h=ac,dragMomentum:p=!0}=l;return{...l,drag:u,dragDirectionLock:o,dragPropagation:c,dragConstraints:d,dragElastic:h,dragMomentum:p}}}function Ys(i,l,u){return(l===!0||l===i)&&(u===null||u===i)}function Kb(i,l=10){let u=null;return Math.abs(i.y)>l?u="y":Math.abs(i.x)>l&&(u="x"),u}class Qb extends qn{constructor(l){super(l),this.removeGroupControls=ze,this.removeListeners=ze,this.controls=new Zb(l)}mount(){const{dragControls:l}=this.node.getProps();l&&(this.removeGroupControls=l.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ze}unmount(){this.removeGroupControls(),this.removeListeners()}}const Ry=i=>(l,u)=>{i&&Dt.postRender(()=>i(l,u))};class kb extends qn{constructor(){super(...arguments),this.removePointerDownListener=ze}onPointerDown(l){this.session=new bg(l,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Tg(this.node)})}createPanHandlers(){const{onPanSessionStart:l,onPanStart:u,onPan:o,onPanEnd:c}=this.node.getProps();return{onSessionStart:Ry(l),onStart:Ry(u),onMove:o,onEnd:(d,h)=>{delete this.session,c&&Dt.postRender(()=>c(d,h))}}}mount(){this.removePointerDownListener=fl(this.node.current,"pointerdown",l=>this.onPointerDown(l))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Qs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Oy(i,l){return l.max===l.min?0:i/(l.max-l.min)*100}const ul={correct:(i,l)=>{if(!l.target)return i;if(typeof i=="string")if(at.test(i))i=parseFloat(i);else return i;const u=Oy(i,l.target.x),o=Oy(i,l.target.y);return`${u}% ${o}%`}},Jb={correct:(i,{treeScale:l,projectionDelta:u})=>{const o=i,c=Hn.parse(i);if(c.length>5)return o;const d=Hn.createTransformer(i),h=typeof c[0]!="number"?1:0,p=u.x.scale*l.x,g=u.y.scale*l.y;c[0+h]/=p,c[1+h]/=g;const y=Ot(p,g,.5);return typeof c[2+h]=="number"&&(c[2+h]/=y),typeof c[3+h]=="number"&&(c[3+h]/=y),d(c)}};let Ur=!1;class Pb extends W.Component{componentDidMount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:o,layoutId:c}=this.props,{projection:d}=l;gT(Fb),d&&(u.group&&u.group.add(d),o&&o.register&&c&&o.register(d),Ur&&d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions({...d.options,onExitComplete:()=>this.safeToRemove()})),Qs.hasEverUpdated=!0}getSnapshotBeforeUpdate(l){const{layoutDependency:u,visualElement:o,drag:c,isPresent:d}=this.props,{projection:h}=o;return h&&(h.isPresent=d,Ur=!0,c||l.layoutDependency!==u||u===void 0||l.isPresent!==d?h.willUpdate():this.safeToRemove(),l.isPresent!==d&&(d?h.promote():h.relegate()||Dt.postRender(()=>{const p=h.getStack();(!p||!p.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:l}=this.props.visualElement;l&&(l.root.didUpdate(),Rc.postRender(()=>{!l.currentAnimation&&l.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:o}=this.props,{projection:c}=l;Ur=!0,c&&(c.scheduleCheckAfterUnmount(),u&&u.group&&u.group.remove(c),o&&o.deregister&&o.deregister(c))}safeToRemove(){const{safeToRemove:l}=this.props;l&&l()}render(){return null}}function xg(i){const[l,u]=cT(),o=W.useContext(ep);return J.jsx(Pb,{...i,layoutGroup:o,switchLayoutGroup:W.useContext(ag),isPresent:l,safeToRemove:u})}const Fb={borderRadius:{...ul,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ul,borderTopRightRadius:ul,borderBottomLeftRadius:ul,borderBottomRightRadius:ul,boxShadow:Jb};function Wb(i,l,u){const o=ee(i)?i:ai(i);return o.start(wc("",o,l,u)),o.animation}const $b=(i,l)=>i.depth-l.depth;class Ib{constructor(){this.children=[],this.isDirty=!1}add(l){oc(this.children,l),this.isDirty=!0}remove(l){rc(this.children,l),this.isDirty=!0}forEach(l){this.isDirty&&this.children.sort($b),this.isDirty=!1,this.children.forEach(l)}}function tA(i,l){const u=re.now(),o=({timestamp:c})=>{const d=c-u;d>=l&&(Ln(o),i(d-l))};return Dt.setup(o,!0),()=>Ln(o)}const Eg=["TopLeft","TopRight","BottomLeft","BottomRight"],eA=Eg.length,Cy=i=>typeof i=="string"?parseFloat(i):i,Vy=i=>typeof i=="number"||at.test(i);function nA(i,l,u,o,c,d){c?(i.opacity=Ot(0,u.opacity??1,aA(o)),i.opacityExit=Ot(l.opacity??1,0,iA(o))):d&&(i.opacity=Ot(l.opacity??1,u.opacity??1,o));for(let h=0;h<eA;h++){const p=`border${Eg[h]}Radius`;let g=zy(l,p),y=zy(u,p);if(g===void 0&&y===void 0)continue;g||(g=0),y||(y=0),g===0||y===0||Vy(g)===Vy(y)?(i[p]=Math.max(Ot(Cy(g),Cy(y),o),0),(Ke.test(y)||Ke.test(g))&&(i[p]+="%")):i[p]=y}(l.rotate||u.rotate)&&(i.rotate=Ot(l.rotate||0,u.rotate||0,o))}function zy(i,l){return i[l]!==void 0?i[l]:i.borderRadius}const aA=Mg(0,.5,hp),iA=Mg(.5,.95,ze);function Mg(i,l,u){return o=>o<i?0:o>l?1:u(ml(i,l,o))}function _y(i,l){i.min=l.min,i.max=l.max}function Ce(i,l){_y(i.x,l.x),_y(i.y,l.y)}function Uy(i,l){i.translate=l.translate,i.scale=l.scale,i.originPoint=l.originPoint,i.origin=l.origin}function Ny(i,l,u,o,c){return i-=l,i=Ws(i,1/u,o),c!==void 0&&(i=Ws(i,1/c,o)),i}function lA(i,l=0,u=1,o=.5,c,d=i,h=i){if(Ke.test(l)&&(l=parseFloat(l),l=Ot(h.min,h.max,l/100)-h.min),typeof l!="number")return;let p=Ot(d.min,d.max,o);i===d&&(p-=l),i.min=Ny(i.min,l,u,p,c),i.max=Ny(i.max,l,u,p,c)}function By(i,l,[u,o,c],d,h){lA(i,l[u],l[o],l[c],l.scale,d,h)}const sA=["x","scaleX","originX"],uA=["y","scaleY","originY"];function jy(i,l,u,o){By(i.x,l,sA,u?u.x:void 0,o?o.x:void 0),By(i.y,l,uA,u?u.y:void 0,o?o.y:void 0)}function wy(i){return i.translate===0&&i.scale===1}function Dg(i){return wy(i.x)&&wy(i.y)}function Ly(i,l){return i.min===l.min&&i.max===l.max}function oA(i,l){return Ly(i.x,l.x)&&Ly(i.y,l.y)}function Hy(i,l){return Math.round(i.min)===Math.round(l.min)&&Math.round(i.max)===Math.round(l.max)}function Rg(i,l){return Hy(i.x,l.x)&&Hy(i.y,l.y)}function qy(i){return ae(i.x)/ae(i.y)}function Yy(i,l){return i.translate===l.translate&&i.scale===l.scale&&i.originPoint===l.originPoint}class rA{constructor(){this.members=[]}add(l){oc(this.members,l),l.scheduleRender()}remove(l){if(rc(this.members,l),l===this.prevLead&&(this.prevLead=void 0),l===this.lead){const u=this.members[this.members.length-1];u&&this.promote(u)}}relegate(l){const u=this.members.findIndex(c=>l===c);if(u===0)return!1;let o;for(let c=u;c>=0;c--){const d=this.members[c];if(d.isPresent!==!1){o=d;break}}return o?(this.promote(o),!0):!1}promote(l,u){const o=this.lead;if(l!==o&&(this.prevLead=o,this.lead=l,l.show(),o)){o.instance&&o.scheduleRender(),l.scheduleRender(),l.resumeFrom=o,u&&(l.resumeFrom.preserveOpacity=!0),o.snapshot&&(l.snapshot=o.snapshot,l.snapshot.latestValues=o.animationValues||o.latestValues),l.root&&l.root.isUpdating&&(l.isLayoutDirty=!0);const{crossfade:c}=l.options;c===!1&&o.hide()}}exitAnimationComplete(){this.members.forEach(l=>{const{options:u,resumingFrom:o}=l;u.onExitComplete&&u.onExitComplete(),o&&o.options.onExitComplete&&o.options.onExitComplete()})}scheduleRender(){this.members.forEach(l=>{l.instance&&l.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function cA(i,l,u){let o="";const c=i.x.translate/l.x,d=i.y.translate/l.y,h=u?.z||0;if((c||d||h)&&(o=`translate3d(${c}px, ${d}px, ${h}px) `),(l.x!==1||l.y!==1)&&(o+=`scale(${1/l.x}, ${1/l.y}) `),u){const{transformPerspective:y,rotate:v,rotateX:A,rotateY:x,skewX:U,skewY:w}=u;y&&(o=`perspective(${y}px) ${o}`),v&&(o+=`rotate(${v}deg) `),A&&(o+=`rotateX(${A}deg) `),x&&(o+=`rotateY(${x}deg) `),U&&(o+=`skewX(${U}deg) `),w&&(o+=`skewY(${w}deg) `)}const p=i.x.scale*l.x,g=i.y.scale*l.y;return(p!==1||g!==1)&&(o+=`scale(${p}, ${g})`),o||"none"}const Nr=["","X","Y","Z"],fA=1e3;let hA=0;function Br(i,l,u,o){const{latestValues:c}=l;c[i]&&(u[i]=c[i],l.setStaticValue(i,0),o&&(o[i]=0))}function Og(i){if(i.hasCheckedOptimisedAppear=!0,i.root===i)return;const{visualElement:l}=i.options;if(!l)return;const u=dg(l);if(window.MotionHasOptimisedAnimation(u,"transform")){const{layout:c,layoutId:d}=i.options;window.MotionCancelOptimisedAnimation(u,"transform",Dt,!(c||d))}const{parent:o}=i;o&&!o.hasCheckedOptimisedAppear&&Og(o)}function Cg({attachResizeListener:i,defaultParent:l,measureScroll:u,checkIsScrollRoot:o,resetTransform:c}){return class{constructor(h={},p=l?.()){this.id=hA++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(yA),this.nodes.forEach(SA),this.nodes.forEach(TA),this.nodes.forEach(pA)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=h,this.root=p?p.root||p:this,this.path=p?[...p.path,p]:[],this.parent=p,this.depth=p?p.depth+1:0;for(let g=0;g<this.path.length;g++)this.path[g].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ib)}addEventListener(h,p){return this.eventHandlers.has(h)||this.eventHandlers.set(h,new hc),this.eventHandlers.get(h).add(p)}notifyListeners(h,...p){const g=this.eventHandlers.get(h);g&&g.notify(...p)}hasListeners(h){return this.eventHandlers.has(h)}mount(h){if(this.instance)return;this.isSVG=Zp(h)&&!uT(h),this.instance=h;const{layoutId:p,layout:g,visualElement:y}=this.options;if(y&&!y.current&&y.mount(h),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(g||p)&&(this.isLayoutDirty=!0),i){let v,A=0;const x=()=>this.root.updateBlockedByResize=!1;Dt.read(()=>{A=window.innerWidth}),i(h,()=>{const U=window.innerWidth;U!==A&&(A=U,this.root.updateBlockedByResize=!0,v&&v(),v=tA(x,250),Qs.hasAnimatedSinceResize&&(Qs.hasAnimatedSinceResize=!1,this.nodes.forEach(Zy)))})}p&&this.root.registerSharedNode(p,this),this.options.animate!==!1&&y&&(p||g)&&this.addEventListener("didUpdate",({delta:v,hasLayoutChanged:A,hasRelativeLayoutChanged:x,layout:U})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const w=this.options.transition||y.getDefaultTransition()||MA,{onLayoutAnimationStart:L,onLayoutAnimationComplete:Y}=y.getProps(),q=!this.targetLayout||!Rg(this.targetLayout,U),X=!A&&x;if(this.options.layoutRoot||this.resumeFrom||X||A&&(q||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const H={...Mc(w,"layout"),onPlay:L,onComplete:Y};(y.shouldReduceMotion||this.options.layoutRoot)&&(H.delay=0,H.type=!1),this.startAnimation(H),this.setAnimationOrigin(v,X)}else A||Zy(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=U})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const h=this.getStack();h&&h.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Ln(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(bA),this.animationId++)}getTransformTemplate(){const{visualElement:h}=this.options;return h&&h.getProps().transformTemplate}willUpdate(h=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Og(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let v=0;v<this.path.length;v++){const A=this.path[v];A.shouldResetTransform=!0,A.updateScroll("snapshot"),A.options.layoutRoot&&A.willUpdate(!1)}const{layoutId:p,layout:g}=this.options;if(p===void 0&&!g)return;const y=this.getTransformTemplate();this.prevTransformTemplateValue=y?y(this.latestValues,""):void 0,this.updateSnapshot(),h&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Gy);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Xy);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(vA),this.nodes.forEach(dA),this.nodes.forEach(mA)):this.nodes.forEach(Xy),this.clearAllSnapshots();const p=re.now();Ft.delta=cn(0,1e3/60,p-Ft.timestamp),Ft.timestamp=p,Ft.isProcessing=!0,xr.update.process(Ft),xr.preRender.process(Ft),xr.render.process(Ft),Ft.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Rc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(gA),this.sharedNodes.forEach(AA)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Dt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Dt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!ae(this.snapshot.measuredBox.x)&&!ae(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let g=0;g<this.path.length;g++)this.path[g].updateScroll();const h=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Nt(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:p}=this.options;p&&p.notify("LayoutMeasure",this.layout.layoutBox,h?h.layoutBox:void 0)}updateScroll(h="measure"){let p=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===h&&(p=!1),p&&this.instance){const g=o(this.instance);this.scroll={animationId:this.root.animationId,phase:h,isRoot:g,offset:u(this.instance),wasRoot:this.scroll?this.scroll.isRoot:g}}}resetTransform(){if(!c)return;const h=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,p=this.projectionDelta&&!Dg(this.projectionDelta),g=this.getTransformTemplate(),y=g?g(this.latestValues,""):void 0,v=y!==this.prevTransformTemplateValue;h&&this.instance&&(p||sa(this.latestValues)||v)&&(c(this.instance,y),this.shouldResetTransform=!1,this.scheduleRender())}measure(h=!0){const p=this.measurePageBox();let g=this.removeElementScroll(p);return h&&(g=this.removeTransform(g)),DA(g),{animationId:this.root.animationId,measuredBox:p,layoutBox:g,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:h}=this.options;if(!h)return Nt();const p=h.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(RA))){const{scroll:y}=this.root;y&&(Ia(p.x,y.offset.x),Ia(p.y,y.offset.y))}return p}removeElementScroll(h){const p=Nt();if(Ce(p,h),this.scroll?.wasRoot)return p;for(let g=0;g<this.path.length;g++){const y=this.path[g],{scroll:v,options:A}=y;y!==this.root&&v&&A.layoutScroll&&(v.wasRoot&&Ce(p,h),Ia(p.x,v.offset.x),Ia(p.y,v.offset.y))}return p}applyTransform(h,p=!1){const g=Nt();Ce(g,h);for(let y=0;y<this.path.length;y++){const v=this.path[y];!p&&v.options.layoutScroll&&v.scroll&&v!==v.root&&ti(g,{x:-v.scroll.offset.x,y:-v.scroll.offset.y}),sa(v.latestValues)&&ti(g,v.latestValues)}return sa(this.latestValues)&&ti(g,this.latestValues),g}removeTransform(h){const p=Nt();Ce(p,h);for(let g=0;g<this.path.length;g++){const y=this.path[g];if(!y.instance||!sa(y.latestValues))continue;Wr(y.latestValues)&&y.updateSnapshot();const v=Nt(),A=y.measurePageBox();Ce(v,A),jy(p,y.latestValues,y.snapshot?y.snapshot.layoutBox:void 0,v)}return sa(this.latestValues)&&jy(p,this.latestValues),p}setTargetDelta(h){this.targetDelta=h,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(h){this.options={...this.options,...h,crossfade:h.crossfade!==void 0?h.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Ft.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(h=!1){const p=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=p.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=p.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=p.isSharedProjectionDirty);const g=!!this.resumingFrom||this!==p;if(!(h||g&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:v,layoutId:A}=this.options;if(!(!this.layout||!(v||A))){if(this.resolvedRelativeTargetAt=Ft.timestamp,!this.targetDelta&&!this.relativeTarget){const x=this.getClosestProjectingParent();x&&x.layout&&this.animationProgress!==1?(this.relativeParent=x,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Nt(),this.relativeTargetOrigin=Nt(),dl(this.relativeTargetOrigin,this.layout.layoutBox,x.layout.layoutBox),Ce(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Nt(),this.targetWithTransforms=Nt()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Ub(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ce(this.target,this.layout.layoutBox),ug(this.target,this.targetDelta)):Ce(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const x=this.getClosestProjectingParent();x&&!!x.resumingFrom==!!this.resumingFrom&&!x.options.layoutScroll&&x.target&&this.animationProgress!==1?(this.relativeParent=x,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Nt(),this.relativeTargetOrigin=Nt(),dl(this.relativeTargetOrigin,this.target,x.target),Ce(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Wr(this.parent.latestValues)||sg(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const h=this.getLead(),p=!!this.resumingFrom||this!==h;let g=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(g=!1),p&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(g=!1),this.resolvedRelativeTargetAt===Ft.timestamp&&(g=!1),g)return;const{layout:y,layoutId:v}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(y||v))return;Ce(this.layoutCorrected,this.layout.layoutBox);const A=this.treeScale.x,x=this.treeScale.y;KT(this.layoutCorrected,this.treeScale,this.path,p),h.layout&&!h.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(h.target=h.layout.layoutBox,h.targetWithTransforms=Nt());const{target:U}=h;if(!U){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Uy(this.prevProjectionDelta.x,this.projectionDelta.x),Uy(this.prevProjectionDelta.y,this.projectionDelta.y)),hl(this.projectionDelta,this.layoutCorrected,U,this.latestValues),(this.treeScale.x!==A||this.treeScale.y!==x||!Yy(this.projectionDelta.x,this.prevProjectionDelta.x)||!Yy(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",U))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(h=!0){if(this.options.visualElement?.scheduleRender(),h){const p=this.getStack();p&&p.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ei(),this.projectionDelta=ei(),this.projectionDeltaWithTransform=ei()}setAnimationOrigin(h,p=!1){const g=this.snapshot,y=g?g.latestValues:{},v={...this.latestValues},A=ei();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!p;const x=Nt(),U=g?g.source:void 0,w=this.layout?this.layout.source:void 0,L=U!==w,Y=this.getStack(),q=!Y||Y.members.length<=1,X=!!(L&&!q&&this.options.crossfade===!0&&!this.path.some(EA));this.animationProgress=0;let H;this.mixTargetDelta=Q=>{const B=Q/1e3;Ky(A.x,h.x,B),Ky(A.y,h.y,B),this.setTargetDelta(A),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(dl(x,this.layout.layoutBox,this.relativeParent.layout.layoutBox),xA(this.relativeTarget,this.relativeTargetOrigin,x,B),H&&oA(this.relativeTarget,H)&&(this.isProjectionDirty=!1),H||(H=Nt()),Ce(H,this.relativeTarget)),L&&(this.animationValues=v,nA(v,y,this.latestValues,B,X,q)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=B},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(h){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(Ln(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Dt.update(()=>{Qs.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ai(0)),this.currentAnimation=Wb(this.motionValue,[0,1e3],{...h,velocity:0,isSync:!0,onUpdate:p=>{this.mixTargetDelta(p),h.onUpdate&&h.onUpdate(p)},onStop:()=>{},onComplete:()=>{h.onComplete&&h.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const h=this.getStack();h&&h.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(fA),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const h=this.getLead();let{targetWithTransforms:p,target:g,layout:y,latestValues:v}=h;if(!(!p||!g||!y)){if(this!==h&&this.layout&&y&&Vg(this.options.animationType,this.layout.layoutBox,y.layoutBox)){g=this.target||Nt();const A=ae(this.layout.layoutBox.x);g.x.min=h.target.x.min,g.x.max=g.x.min+A;const x=ae(this.layout.layoutBox.y);g.y.min=h.target.y.min,g.y.max=g.y.min+x}Ce(p,g),ti(p,v),hl(this.projectionDeltaWithTransform,this.layoutCorrected,p,v)}}registerSharedNode(h,p){this.sharedNodes.has(h)||this.sharedNodes.set(h,new rA),this.sharedNodes.get(h).add(p);const y=p.options.initialPromotionConfig;p.promote({transition:y?y.transition:void 0,preserveFollowOpacity:y&&y.shouldPreserveFollowOpacity?y.shouldPreserveFollowOpacity(p):void 0})}isLead(){const h=this.getStack();return h?h.lead===this:!0}getLead(){const{layoutId:h}=this.options;return h?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:h}=this.options;return h?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:h}=this.options;if(h)return this.root.sharedNodes.get(h)}promote({needsReset:h,transition:p,preserveFollowOpacity:g}={}){const y=this.getStack();y&&y.promote(this,g),h&&(this.projectionDelta=void 0,this.needsReset=!0),p&&this.setOptions({transition:p})}relegate(){const h=this.getStack();return h?h.relegate(this):!1}resetSkewAndRotation(){const{visualElement:h}=this.options;if(!h)return;let p=!1;const{latestValues:g}=h;if((g.z||g.rotate||g.rotateX||g.rotateY||g.rotateZ||g.skewX||g.skewY)&&(p=!0),!p)return;const y={};g.z&&Br("z",h,y,this.animationValues);for(let v=0;v<Nr.length;v++)Br(`rotate${Nr[v]}`,h,y,this.animationValues),Br(`skew${Nr[v]}`,h,y,this.animationValues);h.render();for(const v in y)h.setStaticValue(v,y[v]),this.animationValues&&(this.animationValues[v]=y[v]);h.scheduleRender()}applyProjectionStyles(h,p){if(!this.instance||this.isSVG)return;if(!this.isVisible){h.visibility="hidden";return}const g=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,h.visibility="",h.opacity="",h.pointerEvents=Ks(p?.pointerEvents)||"",h.transform=g?g(this.latestValues,""):"none";return}const y=this.getLead();if(!this.projectionDelta||!this.layout||!y.target){this.options.layoutId&&(h.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,h.pointerEvents=Ks(p?.pointerEvents)||""),this.hasProjected&&!sa(this.latestValues)&&(h.transform=g?g({},""):"none",this.hasProjected=!1);return}h.visibility="";const v=y.animationValues||y.latestValues;this.applyTransformsToTarget();let A=cA(this.projectionDeltaWithTransform,this.treeScale,v);g&&(A=g(v,A)),h.transform=A;const{x,y:U}=this.projectionDelta;h.transformOrigin=`${x.origin*100}% ${U.origin*100}% 0`,y.animationValues?h.opacity=y===this?v.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:v.opacityExit:h.opacity=y===this?v.opacity!==void 0?v.opacity:"":v.opacityExit!==void 0?v.opacityExit:0;for(const w in vl){if(v[w]===void 0)continue;const{correct:L,applyTo:Y,isCSSVariable:q}=vl[w],X=A==="none"?v[w]:L(v[w],y);if(Y){const H=Y.length;for(let Q=0;Q<H;Q++)h[Y[Q]]=X}else q?this.options.visualElement.renderState.vars[w]=X:h[w]=X}this.options.layoutId&&(h.pointerEvents=y===this?Ks(p?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(h=>h.currentAnimation?.stop()),this.root.nodes.forEach(Gy),this.root.sharedNodes.clear()}}}function dA(i){i.updateLayout()}function mA(i){const l=i.resumeFrom?.snapshot||i.snapshot;if(i.isLead()&&i.layout&&l&&i.hasListeners("didUpdate")){const{layoutBox:u,measuredBox:o}=i.layout,{animationType:c}=i.options,d=l.source!==i.layout.source;c==="size"?Ve(v=>{const A=d?l.measuredBox[v]:l.layoutBox[v],x=ae(A);A.min=u[v].min,A.max=A.min+x}):Vg(c,l.layoutBox,u)&&Ve(v=>{const A=d?l.measuredBox[v]:l.layoutBox[v],x=ae(u[v]);A.max=A.min+x,i.relativeTarget&&!i.currentAnimation&&(i.isProjectionDirty=!0,i.relativeTarget[v].max=i.relativeTarget[v].min+x)});const h=ei();hl(h,u,l.layoutBox);const p=ei();d?hl(p,i.applyTransform(o,!0),l.measuredBox):hl(p,u,l.layoutBox);const g=!Dg(h);let y=!1;if(!i.resumeFrom){const v=i.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:A,layout:x}=v;if(A&&x){const U=Nt();dl(U,l.layoutBox,A.layoutBox);const w=Nt();dl(w,u,x.layoutBox),Rg(U,w)||(y=!0),v.options.layoutRoot&&(i.relativeTarget=w,i.relativeTargetOrigin=U,i.relativeParent=v)}}}i.notifyListeners("didUpdate",{layout:u,snapshot:l,delta:p,layoutDelta:h,hasLayoutChanged:g,hasRelativeLayoutChanged:y})}else if(i.isLead()){const{onExitComplete:u}=i.options;u&&u()}i.options.transition=void 0}function yA(i){i.parent&&(i.isProjecting()||(i.isProjectionDirty=i.parent.isProjectionDirty),i.isSharedProjectionDirty||(i.isSharedProjectionDirty=!!(i.isProjectionDirty||i.parent.isProjectionDirty||i.parent.isSharedProjectionDirty)),i.isTransformDirty||(i.isTransformDirty=i.parent.isTransformDirty))}function pA(i){i.isProjectionDirty=i.isSharedProjectionDirty=i.isTransformDirty=!1}function gA(i){i.clearSnapshot()}function Gy(i){i.clearMeasurements()}function Xy(i){i.isLayoutDirty=!1}function vA(i){const{visualElement:l}=i.options;l&&l.getProps().onBeforeLayoutMeasure&&l.notify("BeforeLayoutMeasure"),i.resetTransform()}function Zy(i){i.finishAnimation(),i.targetDelta=i.relativeTarget=i.target=void 0,i.isProjectionDirty=!0}function SA(i){i.resolveTargetDelta()}function TA(i){i.calcProjection()}function bA(i){i.resetSkewAndRotation()}function AA(i){i.removeLeadSnapshot()}function Ky(i,l,u){i.translate=Ot(l.translate,0,u),i.scale=Ot(l.scale,1,u),i.origin=l.origin,i.originPoint=l.originPoint}function Qy(i,l,u,o){i.min=Ot(l.min,u.min,o),i.max=Ot(l.max,u.max,o)}function xA(i,l,u,o){Qy(i.x,l.x,u.x,o),Qy(i.y,l.y,u.y,o)}function EA(i){return i.animationValues&&i.animationValues.opacityExit!==void 0}const MA={duration:.45,ease:[.4,0,.1,1]},ky=i=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(i),Jy=ky("applewebkit/")&&!ky("chrome/")?Math.round:ze;function Py(i){i.min=Jy(i.min),i.max=Jy(i.max)}function DA(i){Py(i.x),Py(i.y)}function Vg(i,l,u){return i==="position"||i==="preserve-aspect"&&!_b(qy(l),qy(u),.2)}function RA(i){return i!==i.root&&i.scroll?.wasRoot}const OA=Cg({attachResizeListener:(i,l)=>Sl(i,"resize",l),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),jr={current:void 0},zg=Cg({measureScroll:i=>({x:i.scrollLeft,y:i.scrollTop}),defaultParent:()=>{if(!jr.current){const i=new OA({});i.mount(window),i.setOptions({layoutScroll:!0}),jr.current=i}return jr.current},resetTransform:(i,l)=>{i.style.transform=l!==void 0?l:"none"},checkIsScrollRoot:i=>window.getComputedStyle(i).position==="fixed"}),CA={pan:{Feature:kb},drag:{Feature:Qb,ProjectionNode:zg,MeasureLayout:xg}};function Fy(i,l,u){const{props:o}=i;i.animationState&&o.whileHover&&i.animationState.setActive("whileHover",u==="Start");const c="onHover"+u,d=o[c];d&&Dt.postRender(()=>d(l,xl(l)))}class VA extends qn{mount(){const{current:l}=this.node;l&&(this.unmount=nT(l,(u,o)=>(Fy(this.node,o,"Start"),c=>Fy(this.node,c,"End"))))}unmount(){}}class zA extends qn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let l=!1;try{l=this.node.current.matches(":focus-visible")}catch{l=!0}!l||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Tl(Sl(this.node.current,"focus",()=>this.onFocus()),Sl(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Wy(i,l,u){const{props:o}=i;if(i.current instanceof HTMLButtonElement&&i.current.disabled)return;i.animationState&&o.whileTap&&i.animationState.setActive("whileTap",u==="Start");const c="onTap"+(u==="End"?"":u),d=o[c];d&&Dt.postRender(()=>d(l,xl(l)))}class _A extends qn{mount(){const{current:l}=this.node;l&&(this.unmount=sT(l,(u,o)=>(Wy(this.node,o,"Start"),(c,{success:d})=>Wy(this.node,c,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const ic=new WeakMap,wr=new WeakMap,UA=i=>{const l=ic.get(i.target);l&&l(i)},NA=i=>{i.forEach(UA)};function BA({root:i,...l}){const u=i||document;wr.has(u)||wr.set(u,{});const o=wr.get(u),c=JSON.stringify(l);return o[c]||(o[c]=new IntersectionObserver(NA,{root:i,...l})),o[c]}function jA(i,l,u){const o=BA(l);return ic.set(i,u),o.observe(i),()=>{ic.delete(i),o.unobserve(i)}}const wA={some:0,all:1};class LA extends qn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:l={}}=this.node.getProps(),{root:u,margin:o,amount:c="some",once:d}=l,h={root:u?u.current:void 0,rootMargin:o,threshold:typeof c=="number"?c:wA[c]},p=g=>{const{isIntersecting:y}=g;if(this.isInView===y||(this.isInView=y,d&&!y&&this.hasEnteredView))return;y&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",y);const{onViewportEnter:v,onViewportLeave:A}=this.node.getProps(),x=y?v:A;x&&x(g)};return jA(this.node.current,h,p)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:l,prevProps:u}=this.node;["amount","margin","root"].some(HA(l,u))&&this.startObserver()}unmount(){}}function HA({viewport:i={}},{viewport:l={}}={}){return u=>i[u]!==l[u]}const qA={inView:{Feature:LA},tap:{Feature:_A},focus:{Feature:zA},hover:{Feature:VA}},YA={layout:{ProjectionNode:zg,MeasureLayout:xg}},GA={...Db,...qA,...CA,...YA},rn=GT(GA,eb);/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const XA=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ZA=i=>i.replace(/^([A-Z])|[\s-_]+(\w)/g,(l,u,o)=>o?o.toUpperCase():u.toLowerCase()),$y=i=>{const l=ZA(i);return l.charAt(0).toUpperCase()+l.slice(1)},_g=(...i)=>i.filter((l,u,o)=>!!l&&l.trim()!==""&&o.indexOf(l)===u).join(" ").trim(),KA=i=>{for(const l in i)if(l.startsWith("aria-")||l==="role"||l==="title")return!0};/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var QA={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kA=W.forwardRef(({color:i="currentColor",size:l=24,strokeWidth:u=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:h,...p},g)=>W.createElement("svg",{ref:g,...QA,width:l,height:l,stroke:i,strokeWidth:o?Number(u)*24/Number(l):u,className:_g("lucide",c),...!d&&!KA(p)&&{"aria-hidden":"true"},...p},[...h.map(([y,v])=>W.createElement(y,v)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fa=(i,l)=>{const u=W.forwardRef(({className:o,...c},d)=>W.createElement(kA,{ref:d,iconNode:l,className:_g(`lucide-${XA($y(i))}`,`lucide-${i}`,o),...c}));return u.displayName=$y(i),u};/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const JA=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],PA=fa("download",JA);/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FA=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-6a2 2 0 0 1 2.582 0l7 6A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"r6nss1"}]],WA=fa("house",FA);/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $A=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],Iy=fa("refresh-cw",$A);/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IA=[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]],t2=fa("sparkles",IA);/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e2=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],tp=fa("upload",e2);/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n2=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],a2=fa("user",n2);/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i2=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],l2=fa("zap",i2),Gs={baseURL:"https://openrouter.ai/api/v1",model:"google/gemini-2.5-flash-image-preview",maxTokens:1e3,headers:{"Content-Type":"application/json","HTTP-Referer":window.location.origin,"X-Title":"StyleMe Fashion Preview"}},s2=()=>"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",u2=i=>{let l="Please generate an image that shows the person from the first image wearing the clothing items from the other images. ";const u=[];return i.head&&u.push("the headwear/hat from the clothing images"),i.torso&&u.push("the top/shirt/jacket from the clothing images"),i.legs&&u.push("the bottom/pants/skirt from the clothing images"),u.length>0&&(l+=`The person should be wearing: ${u.join(", ")}. `),l+="Create a realistic fashion preview where the clothing fits naturally on the person. ",l+="Maintain the person's original pose and background. ",l+="Make sure the clothing looks properly fitted and styled. ",l+="Generate and return the final styled image.",l},o2=(i,l)=>{const u=[i];return Object.values(l).forEach(o=>{o&&u.push(o)}),u};function r2(){const[i,l]=W.useState(null),[u,o]=W.useState({head:null,torso:null,legs:null}),[c,d]=W.useState(null),[h,p]=W.useState(!1),[g,y]=W.useState(null),v=L=>{const Y=L.target.files[0];if(Y){const q=new FileReader;q.onload=X=>l(X.target.result),q.readAsDataURL(Y)}},A=(L,Y)=>{const q=Y.target.files[0];if(q){const X=new FileReader;X.onload=H=>{o(Q=>({...Q,[L]:H.target.result}))},X.readAsDataURL(q)}},x=async()=>{if(!i){y("Carica prima la tua foto!");return}if(!Object.values(u).some(Y=>Y!==null)){y("Carica almeno un indumento!");return}p(!0),y(null);try{const Y=s2(),q=o2(i,u),X=u2(u),H=await fetch(`${Gs.baseURL}/chat/completions`,{method:"POST",headers:{Authorization:`Bearer ${Y}`,...Gs.headers},body:JSON.stringify({model:Gs.model,messages:[{role:"user",content:[{type:"text",text:X},...q.map(B=>({type:"image_url",image_url:{url:B}}))]}],max_tokens:Gs.maxTokens})});if(!H.ok)throw new Error(`Errore API: ${H.status}`);const Q=await H.json();if(console.log("Risposta API completa:",Q),Q.choices&&Q.choices[0]&&Q.choices[0].message){const B=Q.choices[0].message;console.log("Messaggio ricevuto:",B);let tt=!1;if(B.content&&Array.isArray(B.content))for(const it of B.content){if(console.log("Parte del contenuto:",it),it.type==="image_url"&&it.image_url&&it.image_url.url){d(it.image_url.url),tt=!0;break}if(it.type==="text"&&it.text){const F=it.text.match(/data:image\/[^;]+;base64,[A-Za-z0-9+/=]+/);if(F){d(F[0]),tt=!0;break}}}if(!tt&&typeof B.content=="string"){console.log("Contenuto stringa:",B.content);const it=B.content.match(/data:image\/[^;]+;base64,[A-Za-z0-9+/=]+/);it&&(d(it[0]),tt=!0)}!tt&&Q.choices[0].generated_images&&(console.log("Immagini generate trovate:",Q.choices[0].generated_images),Q.choices[0].generated_images.length>0&&(d(Q.choices[0].generated_images[0]),tt=!0)),!tt&&Q.images&&Q.images.length>0&&(console.log("Campo images trovato:",Q.images),d(Q.images[0]),tt=!0),tt||(console.log("Struttura completa della risposta:",JSON.stringify(Q,null,2)),console.warn("Usando fallback: foto originale"),d(i),y("L'API ha processato la richiesta ma l'immagine generata non è stata trovata nel formato atteso. Controlla la console del browser per i dettagli tecnici."))}else throw console.log("Struttura risposta non valida:",Q),new Error("Risposta API non valida o vuota")}catch(Y){y(Y.message)}finally{p(!1)}},U=()=>{if(c){const L=document.createElement("a");L.href=c,L.download="styleme-preview.png",L.click()}},w=()=>{l(null),o({head:null,torso:null,legs:null}),d(null),y(null)};return J.jsxs("div",{className:"app",children:[J.jsx("div",{className:"background-pattern"}),J.jsx("header",{className:"header",children:J.jsxs(rn.div,{className:"logo",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},children:[J.jsx(t2,{className:"logo-icon"}),J.jsx("h1",{children:"StyleMe"}),J.jsx("p",{children:"AI Fashion Preview"})]})}),J.jsx("main",{className:"main-content",children:c?J.jsxs(rn.div,{className:"result-section",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.6},children:[J.jsx("div",{className:"result-header",children:J.jsx("h2",{children:"La Tua Preview di Stile"})}),J.jsx("div",{className:"result-image",children:J.jsx("img",{src:c,alt:"Style Preview"})}),J.jsxs("div",{className:"result-actions",children:[J.jsxs(rn.button,{className:"download-btn",onClick:U,whileHover:{scale:1.05},whileTap:{scale:.95},children:[J.jsx(PA,{className:"btn-icon"}),"Scarica Immagine"]}),J.jsxs(rn.button,{className:"home-btn",onClick:w,whileHover:{scale:1.05},whileTap:{scale:.95},children:[J.jsx(WA,{className:"btn-icon"}),"Nuova Prova"]}),J.jsxs(rn.button,{className:"retry-btn",onClick:x,whileHover:{scale:1.05},whileTap:{scale:.95},children:[J.jsx(Iy,{className:"btn-icon"}),"Rigenera"]})]})]}):J.jsxs("div",{className:"upload-section",children:[J.jsxs(rn.div,{className:"upload-card user-photo-card",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},children:[J.jsxs("div",{className:"upload-header",children:[J.jsx(a2,{className:"upload-icon"}),J.jsx("h3",{children:"La Tua Foto"})]}),i?J.jsxs("div",{className:"photo-preview",children:[J.jsx("img",{src:i,alt:"User"}),J.jsx("button",{className:"change-photo-btn",onClick:()=>document.getElementById("user-photo").click(),children:"Cambia Foto"})]}):J.jsxs("label",{className:"upload-area",htmlFor:"user-photo",children:[J.jsx(tp,{className:"upload-placeholder-icon"}),J.jsx("span",{children:"Carica la tua foto"}),J.jsx("small",{children:"Foto intera o mezzo busto"})]}),J.jsx("input",{id:"user-photo",type:"file",accept:"image/*",onChange:v,style:{display:"none"}})]}),J.jsx("div",{className:"clothing-slots",children:[{key:"head",label:"Testa",icon:"👑"},{key:"torso",label:"Busto",icon:"👕"},{key:"legs",label:"Gambe",icon:"👖"}].map((L,Y)=>J.jsxs(rn.div,{className:"upload-card clothing-card",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5,delay:Y*.1},children:[J.jsxs("div",{className:"upload-header",children:[J.jsx("span",{className:"clothing-emoji",children:L.icon}),J.jsx("h4",{children:L.label})]}),u[L.key]?J.jsxs("div",{className:"clothing-preview",children:[J.jsx("img",{src:u[L.key],alt:L.label}),J.jsx("button",{className:"change-clothing-btn",onClick:()=>document.getElementById(`clothing-${L.key}`).click(),children:"Cambia"})]}):J.jsxs("label",{className:"upload-area small",htmlFor:`clothing-${L.key}`,children:[J.jsx(tp,{className:"upload-placeholder-icon small"}),J.jsxs("span",{children:["Carica ",L.label.toLowerCase()]})]}),J.jsx("input",{id:`clothing-${L.key}`,type:"file",accept:"image/*",onChange:q=>A(L.key,q),style:{display:"none"}})]},L.key))}),J.jsx(rn.button,{className:"generate-btn",onClick:x,disabled:h||!i,whileHover:{scale:1.05},whileTap:{scale:.95},children:h?J.jsxs(J.Fragment,{children:[J.jsx(Iy,{className:"btn-icon spinning"}),"Generando..."]}):J.jsxs(J.Fragment,{children:[J.jsx(l2,{className:"btn-icon"}),"Genera Preview"]})}),g&&J.jsx(rn.div,{className:"error-message",initial:{opacity:0},animate:{opacity:1},children:g})]})})]})}c1.createRoot(document.getElementById("root")).render(J.jsx(W.StrictMode,{children:J.jsx(r2,{})}));
