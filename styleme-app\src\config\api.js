// API Configuration for OpenRouter
export const API_CONFIG = {
  baseURL: 'https://openrouter.ai/api/v1',
  model: 'google/gemini-2.5-flash-image-preview',
  maxTokens: 1000,
  headers: {
    'Content-Type': 'application/json',
    'HTTP-Referer': window.location.origin,
    'X-Title': 'StyleMe Fashion Preview'
  }
}

// Utility function to get API key
export const getApiKey = () => {
  const apiKey = import.meta.env.VITE_OPENROUTER_API_KEY
  if (!apiKey) {
    throw new Error('API Key non configurata. Aggiungi VITE_OPENROUTER_API_KEY nelle variabili d\'ambiente.')
  }
  return apiKey
}

// Utility function to create prompt
export const createFashionPrompt = (clothingItems) => {
  let prompt = "Generate a new image showing the person from the first image wearing the clothing items from the additional images. "
  prompt += "IMPORTANT: You must create and return a new image, not just describe it. "
  prompt += "The person should be wearing: "

  if (clothingItems.head) prompt += "the headwear/hat from one of the clothing images, "
  if (clothingItems.torso) prompt += "the top/shirt/jacket from one of the clothing images, "
  if (clothingItems.legs) prompt += "the bottom/pants/skirt from one of the clothing images, "

  prompt += "Make it look natural and realistic, maintaining the person's pose and background. "
  prompt += "The clothing should fit properly on the person and look professionally styled. "
  prompt += "Return the generated image as the response."

  return prompt
}

// Utility function to prepare images for API
export const prepareImagesForAPI = (userPhoto, clothingItems) => {
  const images = [userPhoto]
  Object.values(clothingItems).forEach(item => {
    if (item) images.push(item)
  })
  return images
}
