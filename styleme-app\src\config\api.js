// API Configuration for OpenRouter
export const API_CONFIG = {
  baseURL: 'https://openrouter.ai/api/v1',
  model: 'google/gemini-2.5-flash-image-preview',
  maxTokens: 1000,
  headers: {
    'Content-Type': 'application/json',
    'HTTP-Referer': window.location.origin,
    'X-Title': 'StyleMe Fashion Preview'
  }
}

// Utility function to get API key
export const getApiKey = () => {
  const apiKey = import.meta.env.VITE_OPENROUTER_API_KEY
  if (!apiKey) {
    throw new Error('API Key non configurata. Aggiungi VITE_OPENROUTER_API_KEY nelle variabili d\'ambiente.')
  }
  return apiKey
}

// Utility function to create prompt
export const createFashionPrompt = (clothingItems) => {
  let prompt = "Please generate an image that shows the person from the first image wearing the clothing items from the other images. "

  // Specifica esattamente cosa indossare
  const itemsToWear = []
  if (clothingItems.head) itemsToWear.push("the headwear/hat from the clothing images")
  if (clothingItems.torso) itemsToWear.push("the top/shirt/jacket from the clothing images")
  if (clothingItems.legs) itemsToWear.push("the bottom/pants/skirt from the clothing images")

  if (itemsToWear.length > 0) {
    prompt += `The person should be wearing: ${itemsToWear.join(", ")}. `
  }

  prompt += "Create a realistic fashion preview where the clothing fits naturally on the person. "
  prompt += "Maintain the person's original pose and background. "
  prompt += "Make sure the clothing looks properly fitted and styled. "
  prompt += "Generate and return the final styled image."

  return prompt
}

// Utility function to prepare images for API
export const prepareImagesForAPI = (userPhoto, clothingItems) => {
  const images = [userPhoto]
  Object.values(clothingItems).forEach(item => {
    if (item) images.push(item)
  })
  return images
}
