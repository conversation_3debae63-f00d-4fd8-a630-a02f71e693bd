{"hash": "0a97ff0c", "configHash": "95797ce9", "lockfileHash": "46d87b89", "browserHash": "2bcf758b", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "4d1f220c", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "1aeff808", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "28844b99", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "74efc15a", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "36291fef", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "61cfcfc9", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "355e4e7c", "needsInterop": true}}, "chunks": {"chunk-ZTDXEEBU": {"file": "chunk-ZTDXEEBU.js"}, "chunk-I5HANLAN": {"file": "chunk-I5HANLAN.js"}, "chunk-X4QARNC5": {"file": "chunk-X4QARNC5.js"}}}